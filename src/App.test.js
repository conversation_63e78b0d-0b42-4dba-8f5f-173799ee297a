import { render, screen } from '@testing-library/react';

// Mock all components that use external dependencies
jest.mock('./components/SummaryGraph', () => {
  return function MockSummaryGraph() {
    return <div data-testid="summary-graph">Summary Graph</div>;
  };
});

jest.mock('./components/GoalsSection', () => {
  return function MockGoalsSection() {
    return <div data-testid="goals-section">Goals Section</div>;
  };
});

jest.mock('./components/HeaderTimeline', () => {
  return function MockHeaderTimeline() {
    return <div data-testid="header-timeline">Header Timeline</div>;
  };
});

jest.mock('./components/DailyNotesSection', () => {
  return function MockDailyNotesSection() {
    return <div data-testid="daily-notes-section">Daily Notes Section</div>;
  };
});

jest.mock('./components/GoalsEditor', () => {
  return function MockGoalsEditor() {
    return <div data-testid="goals-editor">Goals Editor</div>;
  };
});

jest.mock('./components/FolderSelection', () => {
  return function MockFolderSelection({ handleSelectFolder }) {
    return (
      <div data-testid="folder-selection">
        <p>Welcome! It seems like you haven't selected your daily notes folder yet.</p>
        <button onClick={handleSelectFolder}>Select Folder</button>
      </div>
    );
  };
});

import App from './App';

test('renders daily notes app header', () => {
  render(<App />);
  const headerElement = screen.getByText(/My Daily Notes App/i);
  expect(headerElement).toBeInTheDocument();
});

test('renders folder selection when no folder is selected', () => {
  render(<App />);
  const welcomeText = screen.getByText(/Welcome! It seems like you haven't selected your daily notes folder yet/i);
  expect(welcomeText).toBeInTheDocument();
});

test('renders select folder button', () => {
  render(<App />);
  const selectButton = screen.getByText(/Select Folder/i);
  expect(selectButton).toBeInTheDocument();
});
