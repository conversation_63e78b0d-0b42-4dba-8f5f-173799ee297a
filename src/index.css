/* CSS Custom Properties for theme support */
:root {
  --color-background-default: #ffffff;
  --color-background-paper: #ffffff;
  --color-background-elevated: #f8f9fa;
  --color-background-hover: #f5f5f5;
  --color-text-primary: #212121;
  --color-text-secondary: #757575;
  --color-border-light: #e0e0e0;
  --color-border-main: #bdbdbd;
  --color-primary-main: #2196F3;
  --color-secondary-main: #4CAF50;
  --color-error-main: #f44336;
  --color-success-main: #4caf50;
  --color-warning-main: #ff9800;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  background-color: var(--color-background-default);
  color: var(--color-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

code {
  font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
  background-color: var(--color-background-elevated);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

pre {
  background-color: var(--color-background-elevated);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  border: 1px solid var(--color-border-light);
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* Improved markdown styling */
h1, h2, h3, h4, h5, h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--color-text-primary);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }

p {
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

blockquote {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid var(--color-primary-main);
  background-color: var(--color-background-elevated);
  color: var(--color-text-secondary);
  font-style: italic;
}

ul, ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

li {
  margin-bottom: 0.25rem;
  color: var(--color-text-primary);
}

/* Task list styling */
li input[type="checkbox"] {
  margin-right: 0.5rem;
}

/* Table styling */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

th, td {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid var(--color-border-light);
}

th {
  font-weight: 600;
  background-color: var(--color-background-elevated);
}

/* Link styling */
a {
  color: var(--color-primary-main);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }

  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.375rem; }
  h3 { font-size: 1.125rem; }
  h4 { font-size: 1rem; }
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-elevated);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-main);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}
