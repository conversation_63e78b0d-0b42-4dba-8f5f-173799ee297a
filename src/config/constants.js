// Application constants and configuration
export const APP_CONFIG = {
  name: process.env.REACT_APP_NAME || 'My Daily Notes App',
  version: process.env.REACT_APP_VERSION || '0.1.0',
  debug: process.env.REACT_APP_DEBUG === 'true',
  logLevel: process.env.REACT_APP_LOG_LEVEL || 'info',
};

export const FILE_CONFIG = {
  defaultFolderName: process.env.REACT_APP_DEFAULT_FOLDER_NAME || 'daily-notes',
  goalsFileName: process.env.REACT_APP_GOALS_FILE_NAME || 'goals.md',
  supportedExtensions: ['.md', '.markdown'],
};

export const UI_CONFIG = {
  maxFileNameLength: 255,
  maxContentLength: 1000000, // 1MB
  autoSaveDelay: 2000, // 2 seconds
  defaultWindowSize: {
    width: 1000,
    height: 800,
  },
};

export const VALIDATION_RULES = {
  fileName: {
    minLength: 1,
    maxLength: 255,
    allowedChars: /^[a-zA-Z0-9\-_\s\.]+$/,
  },
  projectName: {
    minLength: 1,
    maxLength: 100,
  },
  taskName: {
    minLength: 1,
    maxLength: 200,
  },
};

// Feature flags
export const FEATURES = {
  fileSystemAccess: 'showDirectoryPicker' in window,
  darkMode: true,
  autoSave: true,
  exportFeatures: true,
};
