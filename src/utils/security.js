/**
 * Security utilities for input sanitization and validation
 */

/**
 * Sanitizes HTML content to prevent XSS attacks
 * @param {string} input - The input string to sanitize
 * @returns {string} - Sanitized string
 */
export const sanitizeHtml = (input) => {
  if (typeof input !== 'string') return '';
  
  // Basic HTML entity encoding
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Sanitizes file names to prevent directory traversal attacks
 * @param {string} fileName - The file name to sanitize
 * @returns {string} - Sanitized file name
 */
export const sanitizeFileName = (fileName) => {
  if (typeof fileName !== 'string') return '';
  
  // Remove directory traversal patterns and dangerous characters
  return fileName
    .replace(/\.\./g, '') // Remove parent directory references
    .replace(/[<>:"|?*]/g, '') // Remove Windows forbidden characters
    .replace(/[\x00-\x1f\x80-\x9f]/g, '') // Remove control characters
    .replace(/^\.+/, '') // Remove leading dots
    .replace(/\.+$/, '') // Remove trailing dots
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
    .substring(0, 255); // Limit length
};

/**
 * Validates and sanitizes markdown content
 * @param {string} content - The markdown content to sanitize
 * @returns {string} - Sanitized markdown content
 */
export const sanitizeMarkdown = (content) => {
  if (typeof content !== 'string') return '';
  
  // Allow markdown but prevent script injection
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/data:(?!image\/)/gi, '') // Remove non-image data URLs
    .substring(0, 1000000); // Limit content size to 1MB
};

/**
 * Validates file content size
 * @param {string|Blob|File} content - The content to validate
 * @param {number} maxSize - Maximum size in bytes (default: 10MB)
 * @returns {boolean} - True if content is within size limit
 */
export const validateFileSize = (content, maxSize = 10 * 1024 * 1024) => {
  let size = 0;
  
  if (typeof content === 'string') {
    size = new Blob([content]).size;
  } else if (content instanceof Blob || content instanceof File) {
    size = content.size;
  } else {
    return false;
  }
  
  return size <= maxSize;
};

/**
 * Validates file type based on extension
 * @param {string} fileName - The file name to validate
 * @param {Array<string>} allowedExtensions - Array of allowed extensions
 * @returns {boolean} - True if file type is allowed
 */
export const validateFileType = (fileName, allowedExtensions = ['.md', '.markdown', '.txt']) => {
  if (typeof fileName !== 'string') return false;
  
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return allowedExtensions.includes(extension);
};

/**
 * Sanitizes URL to prevent open redirect attacks
 * @param {string} url - The URL to sanitize
 * @param {Array<string>} allowedDomains - Array of allowed domains
 * @returns {string|null} - Sanitized URL or null if invalid
 */
export const sanitizeUrl = (url, allowedDomains = []) => {
  if (typeof url !== 'string') return null;
  
  try {
    const urlObj = new URL(url);
    
    // Only allow http/https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return null;
    }
    
    // Check against allowed domains if provided
    if (allowedDomains.length > 0 && !allowedDomains.includes(urlObj.hostname)) {
      return null;
    }
    
    return urlObj.toString();
  } catch (error) {
    return null;
  }
};

/**
 * Rate limiting utility for preventing abuse
 */
export class RateLimiter {
  constructor(maxRequests = 100, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = new Map();
  }
  
  /**
   * Checks if request is allowed
   * @param {string} identifier - Unique identifier for the request source
   * @returns {boolean} - True if request is allowed
   */
  isAllowed(identifier) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // Clean old entries
    for (const [key, timestamps] of this.requests.entries()) {
      const validTimestamps = timestamps.filter(time => time > windowStart);
      if (validTimestamps.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, validTimestamps);
      }
    }
    
    // Check current identifier
    const currentRequests = this.requests.get(identifier) || [];
    const validRequests = currentRequests.filter(time => time > windowStart);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
  
  /**
   * Gets remaining requests for identifier
   * @param {string} identifier - Unique identifier for the request source
   * @returns {number} - Number of remaining requests
   */
  getRemainingRequests(identifier) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    const currentRequests = this.requests.get(identifier) || [];
    const validRequests = currentRequests.filter(time => time > windowStart);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
}

/**
 * Content Security Policy helpers
 */
export const CSP_DIRECTIVES = {
  defaultSrc: ["'self'"],
  scriptSrc: ["'self'", "'unsafe-inline'"], // Note: unsafe-inline needed for React
  styleSrc: ["'self'", "'unsafe-inline'"],
  imgSrc: ["'self'", "data:", "blob:"],
  connectSrc: ["'self'"],
  fontSrc: ["'self'"],
  objectSrc: ["'none'"],
  mediaSrc: ["'self'"],
  frameSrc: ["'none'"],
};

/**
 * Generates CSP header value
 * @param {Object} customDirectives - Custom CSP directives to merge
 * @returns {string} - CSP header value
 */
export const generateCSP = (customDirectives = {}) => {
  const directives = { ...CSP_DIRECTIVES, ...customDirectives };
  
  return Object.entries(directives)
    .map(([key, values]) => {
      const directive = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      return `${directive} ${values.join(' ')}`;
    })
    .join('; ');
};

/**
 * Security headers for Electron app
 */
export const SECURITY_HEADERS = {
  'Content-Security-Policy': generateCSP(),
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
};
