import {
  AppError,
  ERROR_TYPES,
  ERROR_SEVERITY,
  logError,
  handleFileSystemError,
  handleValidationError,
  handleParsingError,
  handleUnexpectedError,
  showErrorToUser,
} from '../errorHandler';

// Mock console methods
const originalConsoleError = console.error;
beforeEach(() => {
  console.error = jest.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
});

describe('Error Handler Utils', () => {
  describe('AppError', () => {
    test('should create an AppError with default values', () => {
      const error = new AppError('Test error');
      
      expect(error.message).toBe('Test error');
      expect(error.name).toBe('AppError');
      expect(error.type).toBe(ERROR_TYPES.UNKNOWN);
      expect(error.severity).toBe(ERROR_SEVERITY.MEDIUM);
      expect(error.originalError).toBeNull();
      expect(error.timestamp).toBeDefined();
    });

    test('should create an AppError with custom values', () => {
      const originalError = new Error('Original');
      const error = new AppError(
        'Custom error',
        ERROR_TYPES.FILE_SYSTEM,
        ERROR_SEVERITY.HIGH,
        originalError
      );
      
      expect(error.message).toBe('Custom error');
      expect(error.type).toBe(ERROR_TYPES.FILE_SYSTEM);
      expect(error.severity).toBe(ERROR_SEVERITY.HIGH);
      expect(error.originalError).toBe(originalError);
    });
  });

  describe('logError', () => {
    test('should log error with context', () => {
      const error = new AppError('Test error', ERROR_TYPES.VALIDATION, ERROR_SEVERITY.LOW);
      logError(error, 'test context');
      
      expect(console.error).toHaveBeenCalled();
    });

    test('should log regular errors', () => {
      const error = new Error('Regular error');
      logError(error, 'test context');
      
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('handleFileSystemError', () => {
    test('should handle NotAllowedError', () => {
      const originalError = new Error('Permission denied');
      originalError.name = 'NotAllowedError';
      
      const appError = handleFileSystemError(originalError, 'test operation');
      
      expect(appError).toBeInstanceOf(AppError);
      expect(appError.message).toBe('Permission denied. Please grant access to the folder.');
      expect(appError.type).toBe(ERROR_TYPES.FILE_SYSTEM);
      expect(appError.severity).toBe(ERROR_SEVERITY.HIGH);
    });

    test('should handle NotFoundError', () => {
      const originalError = new Error('Not found');
      originalError.name = 'NotFoundError';
      
      const appError = handleFileSystemError(originalError, 'test operation');
      
      expect(appError.message).toBe('File or folder not found.');
      expect(appError.type).toBe(ERROR_TYPES.FILE_SYSTEM);
    });

    test('should handle AbortError', () => {
      const originalError = new Error('Aborted');
      originalError.name = 'AbortError';
      
      const appError = handleFileSystemError(originalError, 'test operation');
      
      expect(appError.message).toBe('Operation was cancelled.');
      expect(appError.severity).toBe(ERROR_SEVERITY.LOW);
    });

    test('should handle SecurityError', () => {
      const originalError = new Error('Security error');
      originalError.name = 'SecurityError';
      
      const appError = handleFileSystemError(originalError, 'test operation');
      
      expect(appError.message).toBe('Security error. This operation is not allowed.');
      expect(appError.severity).toBe(ERROR_SEVERITY.HIGH);
    });

    test('should handle unknown errors', () => {
      const originalError = new Error('Unknown error');
      
      const appError = handleFileSystemError(originalError, 'test operation');
      
      expect(appError.message).toBe('Failed to test operation');
      expect(appError.type).toBe(ERROR_TYPES.FILE_SYSTEM);
    });
  });

  describe('handleValidationError', () => {
    test('should handle validation errors', () => {
      const validationErrors = ['Field is required', 'Field is too long'];
      const appError = handleValidationError(validationErrors, 'username');
      
      expect(appError).toBeInstanceOf(AppError);
      expect(appError.message).toBe('Invalid username: Field is required, Field is too long');
      expect(appError.type).toBe(ERROR_TYPES.VALIDATION);
      expect(appError.severity).toBe(ERROR_SEVERITY.LOW);
    });
  });

  describe('handleParsingError', () => {
    test('should handle parsing errors', () => {
      const originalError = new Error('Parse failed');
      const appError = handleParsingError(originalError, 'markdown');
      
      expect(appError).toBeInstanceOf(AppError);
      expect(appError.message).toBe('Failed to parse markdown. Please check the format.');
      expect(appError.type).toBe(ERROR_TYPES.PARSING);
      expect(appError.severity).toBe(ERROR_SEVERITY.MEDIUM);
    });
  });

  describe('handleUnexpectedError', () => {
    test('should handle unexpected errors', () => {
      const originalError = new Error('Unexpected');
      const appError = handleUnexpectedError(originalError, 'component');
      
      expect(appError).toBeInstanceOf(AppError);
      expect(appError.message).toBe('An unexpected error occurred in component. Please try again.');
      expect(appError.type).toBe(ERROR_TYPES.UNKNOWN);
      expect(appError.severity).toBe(ERROR_SEVERITY.HIGH);
    });
  });

  describe('showErrorToUser', () => {
    test('should show critical errors with prefix', () => {
      const mockNotification = jest.fn();
      const error = new AppError('Critical error', ERROR_TYPES.UNKNOWN, ERROR_SEVERITY.CRITICAL);
      
      showErrorToUser(error, mockNotification);
      
      expect(mockNotification).toHaveBeenCalledWith('Critical Error: Critical error');
    });

    test('should show high severity errors with prefix', () => {
      const mockNotification = jest.fn();
      const error = new AppError('High error', ERROR_TYPES.UNKNOWN, ERROR_SEVERITY.HIGH);
      
      showErrorToUser(error, mockNotification);
      
      expect(mockNotification).toHaveBeenCalledWith('Error: High error');
    });

    test('should show regular errors without prefix', () => {
      const mockNotification = jest.fn();
      const error = new AppError('Regular error', ERROR_TYPES.UNKNOWN, ERROR_SEVERITY.MEDIUM);
      
      showErrorToUser(error, mockNotification);
      
      expect(mockNotification).toHaveBeenCalledWith('Regular error');
    });

    test('should handle regular Error objects', () => {
      const mockNotification = jest.fn();
      const error = new Error('Regular error');
      
      showErrorToUser(error, mockNotification);
      
      expect(mockNotification).toHaveBeenCalledWith('An unexpected error occurred');
    });
  });
});
