import {
  mergeStyles,
  createButtonStyle,
  createCardStyle,
  createInputStyle,
  createTextareaStyle,
  createErrorStyle,
  createSuccessStyle,
  createContainerStyle,
  createFlexStyle,
  createGridStyle,
  createSpinnerStyle,
  createModalOverlayStyle,
  createModalContentStyle,
} from '../styles';

describe('Style Utils', () => {
  describe('mergeStyles', () => {
    test('should merge multiple style objects', () => {
      const style1 = { color: 'red', fontSize: '16px' };
      const style2 = { backgroundColor: 'blue', fontSize: '18px' };
      const style3 = { padding: '10px' };
      
      const result = mergeStyles(style1, style2, style3);
      
      expect(result).toEqual({
        color: 'red',
        fontSize: '18px', // Later styles override earlier ones
        backgroundColor: 'blue',
        padding: '10px',
      });
    });

    test('should handle empty arguments', () => {
      const result = mergeStyles();
      expect(result).toEqual({});
    });
  });

  describe('createButtonStyle', () => {
    test('should create primary button style by default', () => {
      const style = createButtonStyle();
      
      expect(style).toHaveProperty('padding');
      expect(style).toHaveProperty('borderRadius');
      expect(style).toHaveProperty('cursor', 'pointer');
      expect(style).toHaveProperty('backgroundColor');
    });

    test('should create secondary button style', () => {
      const style = createButtonStyle('secondary');
      
      expect(style).toHaveProperty('backgroundColor');
      expect(style).toHaveProperty('color');
    });

    test('should merge custom styles', () => {
      const customStyles = { margin: '10px' };
      const style = createButtonStyle('primary', customStyles);
      
      expect(style).toHaveProperty('margin', '10px');
      expect(style).toHaveProperty('padding'); // Base style preserved
    });

    test('should fallback to primary for unknown variants', () => {
      const style = createButtonStyle('unknown-variant');
      
      expect(style).toHaveProperty('backgroundColor');
      expect(style).toHaveProperty('cursor', 'pointer');
    });
  });

  describe('createCardStyle', () => {
    test('should create card style with default properties', () => {
      const style = createCardStyle();
      
      expect(style).toHaveProperty('backgroundColor');
      expect(style).toHaveProperty('borderRadius');
      expect(style).toHaveProperty('boxShadow');
      expect(style).toHaveProperty('padding');
      expect(style).toHaveProperty('marginBottom');
    });

    test('should merge custom styles', () => {
      const customStyles = { border: '1px solid red' };
      const style = createCardStyle(customStyles);
      
      expect(style).toHaveProperty('border', '1px solid red');
      expect(style).toHaveProperty('padding'); // Base style preserved
    });
  });

  describe('createInputStyle', () => {
    test('should create input style with default properties', () => {
      const style = createInputStyle();
      
      expect(style).toHaveProperty('padding');
      expect(style).toHaveProperty('border');
      expect(style).toHaveProperty('borderRadius');
      expect(style).toHaveProperty('width', '100%');
    });
  });

  describe('createTextareaStyle', () => {
    test('should create textarea style with default properties', () => {
      const style = createTextareaStyle();
      
      expect(style).toHaveProperty('padding');
      expect(style).toHaveProperty('border');
      expect(style).toHaveProperty('minHeight', '300px');
      expect(style).toHaveProperty('fontFamily');
    });
  });

  describe('createErrorStyle', () => {
    test('should create error style with error colors', () => {
      const style = createErrorStyle();
      
      expect(style).toHaveProperty('backgroundColor');
      expect(style).toHaveProperty('color');
      expect(style).toHaveProperty('padding');
      expect(style).toHaveProperty('display', 'flex');
    });
  });

  describe('createSuccessStyle', () => {
    test('should create success style with success colors', () => {
      const style = createSuccessStyle();
      
      expect(style).toHaveProperty('backgroundColor');
      expect(style).toHaveProperty('color');
      expect(style).toHaveProperty('padding');
    });
  });

  describe('createContainerStyle', () => {
    test('should create container style with max width', () => {
      const style = createContainerStyle();
      
      expect(style).toHaveProperty('maxWidth', '1000px');
      expect(style).toHaveProperty('margin');
      expect(style).toHaveProperty('fontFamily');
    });
  });

  describe('createFlexStyle', () => {
    test('should create flex style with default values', () => {
      const style = createFlexStyle();
      
      expect(style).toEqual({
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        gap: expect.any(String),
      });
    });

    test('should create flex style with custom values', () => {
      const style = createFlexStyle('column', 'center', 'center', '20px');
      
      expect(style).toEqual({
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '20px',
      });
    });
  });

  describe('createGridStyle', () => {
    test('should create grid style with default values', () => {
      const style = createGridStyle();
      
      expect(style).toEqual({
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: expect.any(String),
      });
    });

    test('should create grid style with custom values', () => {
      const style = createGridStyle('repeat(3, 1fr)', '15px');
      
      expect(style).toEqual({
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '15px',
      });
    });
  });

  describe('createSpinnerStyle', () => {
    test('should create spinner style with default size', () => {
      const style = createSpinnerStyle();
      
      expect(style).toHaveProperty('width', '24px');
      expect(style).toHaveProperty('height', '24px');
      expect(style).toHaveProperty('border');
      expect(style).toHaveProperty('borderRadius');
      expect(style).toHaveProperty('animation');
    });

    test('should create spinner style with custom size', () => {
      const style = createSpinnerStyle('lg');
      
      expect(style).toHaveProperty('width', '32px');
      expect(style).toHaveProperty('height', '32px');
    });
  });

  describe('createModalOverlayStyle', () => {
    test('should create modal overlay style', () => {
      const style = createModalOverlayStyle();
      
      expect(style).toHaveProperty('position', 'fixed');
      expect(style).toHaveProperty('top', 0);
      expect(style).toHaveProperty('zIndex', 1000);
      expect(style).toHaveProperty('display', 'flex');
    });
  });

  describe('createModalContentStyle', () => {
    test('should create modal content style', () => {
      const style = createModalContentStyle();
      
      expect(style).toHaveProperty('backgroundColor');
      expect(style).toHaveProperty('borderRadius');
      expect(style).toHaveProperty('boxShadow');
      expect(style).toHaveProperty('padding');
      expect(style).toHaveProperty('maxWidth', '90vw');
      expect(style).toHaveProperty('maxHeight', '90vh');
    });
  });
});
