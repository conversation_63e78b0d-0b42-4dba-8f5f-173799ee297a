import {
  validateFileName,
  validateProjectName,
  validateTaskName,
  validateDate,
  validatePercent,
} from '../validation';

describe('Validation Utils', () => {
  describe('validateFileName', () => {
    test('should validate correct file names', () => {
      const result = validateFileName('my-file-name');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe('my-file-name');
    });

    test('should reject empty file names', () => {
      const result = validateFileName('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name is required');
    });

    test('should reject null or undefined file names', () => {
      const result1 = validateFileName(null);
      const result2 = validateFileName(undefined);
      
      expect(result1.isValid).toBe(false);
      expect(result2.isValid).toBe(false);
      expect(result1.errors).toContain('File name is required');
      expect(result2.errors).toContain('File name is required');
    });

    test('should reject file names that are too long', () => {
      const longName = 'a'.repeat(300);
      const result = validateFileName(longName);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name is too long');
    });

    test('should reject file names with invalid characters', () => {
      const result = validateFileName('file<>name');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name contains invalid characters');
    });

    test('should trim whitespace', () => {
      const result = validateFileName('  my-file  ');
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toBe('my-file');
    });
  });

  describe('validateProjectName', () => {
    test('should validate correct project names', () => {
      const result = validateProjectName('My Project');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe('My Project');
    });

    test('should reject empty project names', () => {
      const result = validateProjectName('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Project name is required');
    });

    test('should reject project names that are too long', () => {
      const longName = 'a'.repeat(150);
      const result = validateProjectName(longName);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Project name is too long');
    });
  });

  describe('validateTaskName', () => {
    test('should validate correct task names', () => {
      const result = validateTaskName('Complete the feature');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe('Complete the feature');
    });

    test('should reject empty task names', () => {
      const result = validateTaskName('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Task name is required');
    });

    test('should reject task names that are too long', () => {
      const longName = 'a'.repeat(250);
      const result = validateTaskName(longName);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Task name is too long');
    });
  });

  describe('validateDate', () => {
    test('should validate correct dates', () => {
      const result = validateDate('2023-12-25');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe('2023-12-25');
    });

    test('should allow empty dates', () => {
      const result = validateDate('');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe('');
    });

    test('should reject invalid date formats', () => {
      const result = validateDate('invalid-date');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid date format');
    });
  });

  describe('validatePercent', () => {
    test('should validate correct percentages', () => {
      const result = validatePercent('50');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe(50);
    });

    test('should validate numeric percentages', () => {
      const result = validatePercent(75);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized).toBe(75);
    });

    test('should reject non-numeric percentages', () => {
      const result = validatePercent('not-a-number');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Percentage must be a number');
    });

    test('should reject percentages below 0', () => {
      const result = validatePercent(-10);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Percentage must be between 0 and 100');
    });

    test('should reject percentages above 100', () => {
      const result = validatePercent(150);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Percentage must be between 0 and 100');
    });

    test('should clamp percentages to valid range', () => {
      const result1 = validatePercent(-10);
      const result2 = validatePercent(150);
      
      expect(result1.sanitized).toBe(0);
      expect(result2.sanitized).toBe(100);
    });
  });
});
