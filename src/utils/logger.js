import { APP_CONFIG } from '../config/constants';

/**
 * Log levels in order of severity
 */
export const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  CRITICAL: 4,
};

/**
 * Log level names
 */
export const LOG_LEVEL_NAMES = {
  [LOG_LEVELS.DEBUG]: 'DEBUG',
  [LOG_LEVELS.INFO]: 'INFO',
  [LOG_LEVELS.WARN]: 'WARN',
  [LOG_LEVELS.ERROR]: 'ERROR',
  [LOG_LEVELS.CRITICAL]: 'CRITICAL',
};

/**
 * Logger class for structured logging
 */
class Logger {
  constructor() {
    this.level = this.parseLogLevel(APP_CONFIG.logLevel);
    this.logs = [];
    this.maxLogs = 1000; // Keep last 1000 logs in memory
  }

  /**
   * Parses log level from string
   * @param {string} levelStr - Log level string
   * @returns {number} - Log level number
   */
  parseLogLevel(levelStr) {
    const upperLevel = levelStr.toUpperCase();
    for (const [level, name] of Object.entries(LOG_LEVEL_NAMES)) {
      if (name === upperLevel) {
        return parseInt(level);
      }
    }
    return LOG_LEVELS.INFO; // Default to INFO
  }

  /**
   * Creates a log entry
   * @param {number} level - Log level
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   * @returns {Object} - Log entry
   */
  createLogEntry(level, message, meta = {}) {
    return {
      timestamp: new Date().toISOString(),
      level,
      levelName: LOG_LEVEL_NAMES[level],
      message,
      meta,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
  }

  /**
   * Logs a message if it meets the minimum level
   * @param {number} level - Log level
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  log(level, message, meta = {}) {
    if (level < this.level) return;

    const entry = this.createLogEntry(level, message, meta);
    
    // Add to in-memory logs
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift(); // Remove oldest log
    }

    // Console output
    this.outputToConsole(entry);

    // In production, you might want to send logs to a service
    if (level >= LOG_LEVELS.ERROR) {
      this.reportError(entry);
    }
  }

  /**
   * Outputs log entry to console
   * @param {Object} entry - Log entry
   */
  outputToConsole(entry) {
    const { levelName, message, meta, timestamp } = entry;
    const prefix = `[${timestamp}] ${levelName}:`;
    
    switch (entry.level) {
      case LOG_LEVELS.DEBUG:
        if (APP_CONFIG.debug) {
          console.debug(prefix, message, meta);
        }
        break;
      case LOG_LEVELS.INFO:
        console.info(prefix, message, meta);
        break;
      case LOG_LEVELS.WARN:
        console.warn(prefix, message, meta);
        break;
      case LOG_LEVELS.ERROR:
      case LOG_LEVELS.CRITICAL:
        console.error(prefix, message, meta);
        break;
      default:
        console.log(prefix, message, meta);
    }
  }

  /**
   * Reports error to external service (placeholder)
   * @param {Object} entry - Log entry
   */
  reportError(entry) {
    // In a real application, you might send this to:
    // - Sentry
    // - LogRocket
    // - Custom logging service
    // - Local file (in Electron)
    
    if (APP_CONFIG.debug) {
      console.log('Would report error to external service:', entry);
    }
  }

  /**
   * Debug level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  debug(message, meta = {}) {
    this.log(LOG_LEVELS.DEBUG, message, meta);
  }

  /**
   * Info level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  info(message, meta = {}) {
    this.log(LOG_LEVELS.INFO, message, meta);
  }

  /**
   * Warning level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  warn(message, meta = {}) {
    this.log(LOG_LEVELS.WARN, message, meta);
  }

  /**
   * Error level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  error(message, meta = {}) {
    this.log(LOG_LEVELS.ERROR, message, meta);
  }

  /**
   * Critical level logging
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  critical(message, meta = {}) {
    this.log(LOG_LEVELS.CRITICAL, message, meta);
  }

  /**
   * Gets recent logs
   * @param {number} count - Number of logs to return
   * @param {number} minLevel - Minimum log level to include
   * @returns {Array} - Array of log entries
   */
  getRecentLogs(count = 50, minLevel = LOG_LEVELS.INFO) {
    return this.logs
      .filter(log => log.level >= minLevel)
      .slice(-count);
  }

  /**
   * Clears all logs
   */
  clearLogs() {
    this.logs = [];
  }

  /**
   * Sets the minimum log level
   * @param {string|number} level - Log level
   */
  setLevel(level) {
    if (typeof level === 'string') {
      this.level = this.parseLogLevel(level);
    } else {
      this.level = level;
    }
  }

  /**
   * Logs performance metrics
   * @param {string} operation - Operation name
   * @param {number} duration - Duration in milliseconds
   * @param {Object} meta - Additional metadata
   */
  performance(operation, duration, meta = {}) {
    this.info(`Performance: ${operation}`, {
      ...meta,
      duration,
      operation,
      type: 'performance',
    });
  }

  /**
   * Logs user actions for analytics
   * @param {string} action - Action name
   * @param {Object} meta - Additional metadata
   */
  userAction(action, meta = {}) {
    this.info(`User Action: ${action}`, {
      ...meta,
      action,
      type: 'user_action',
    });
  }

  /**
   * Logs file operations
   * @param {string} operation - Operation type (read, write, delete, etc.)
   * @param {string} fileName - File name
   * @param {Object} meta - Additional metadata
   */
  fileOperation(operation, fileName, meta = {}) {
    this.info(`File Operation: ${operation}`, {
      ...meta,
      operation,
      fileName,
      type: 'file_operation',
    });
  }
}

// Create singleton logger instance
const logger = new Logger();

export default logger;

// Export convenience functions
export const debug = (message, meta) => logger.debug(message, meta);
export const info = (message, meta) => logger.info(message, meta);
export const warn = (message, meta) => logger.warn(message, meta);
export const error = (message, meta) => logger.error(message, meta);
export const critical = (message, meta) => logger.critical(message, meta);
export const performance = (operation, duration, meta) => logger.performance(operation, duration, meta);
export const userAction = (action, meta) => logger.userAction(action, meta);
export const fileOperation = (operation, fileName, meta) => logger.fileOperation(operation, fileName, meta);
