import React from 'react';
import logger from './logger';

/**
 * Performance monitoring utilities
 */

/**
 * Performance timer for measuring operation duration
 */
export class PerformanceTimer {
  constructor(name) {
    this.name = name;
    this.startTime = null;
    this.endTime = null;
  }

  /**
   * Starts the timer
   */
  start() {
    this.startTime = performance.now();
    return this;
  }

  /**
   * Ends the timer and logs the result
   * @param {Object} meta - Additional metadata to log
   * @returns {number} - Duration in milliseconds
   */
  end(meta = {}) {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;
    
    logger.performance(this.name, duration, meta);
    
    return duration;
  }

  /**
   * Gets the current duration without ending the timer
   * @returns {number} - Current duration in milliseconds
   */
  getCurrentDuration() {
    if (!this.startTime) return 0;
    return performance.now() - this.startTime;
  }
}

/**
 * Creates and starts a new performance timer
 * @param {string} name - Timer name
 * @returns {PerformanceTimer} - Started timer instance
 */
export const startTimer = (name) => {
  return new PerformanceTimer(name).start();
};

/**
 * Measures the execution time of a function
 * @param {string} name - Operation name
 * @param {Function} fn - Function to measure
 * @param {Object} meta - Additional metadata
 * @returns {Promise|any} - Function result
 */
export const measureFunction = async (name, fn, meta = {}) => {
  const timer = startTimer(name);
  
  try {
    const result = await fn();
    timer.end({ ...meta, success: true });
    return result;
  } catch (error) {
    timer.end({ ...meta, success: false, error: error.message });
    throw error;
  }
};

/**
 * Memory usage monitoring
 */
export class MemoryMonitor {
  constructor() {
    this.measurements = [];
    this.maxMeasurements = 100;
  }

  /**
   * Takes a memory measurement
   * @param {string} label - Label for the measurement
   * @returns {Object} - Memory usage information
   */
  measure(label = 'measurement') {
    const memory = this.getMemoryUsage();
    const measurement = {
      timestamp: Date.now(),
      label,
      ...memory
    };
    
    this.measurements.push(measurement);
    
    // Keep only recent measurements
    if (this.measurements.length > this.maxMeasurements) {
      this.measurements.shift();
    }
    
    // Log if memory usage is high
    if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
      logger.warn('High memory usage detected', measurement);
    }
    
    return measurement;
  }

  /**
   * Gets current memory usage
   * @returns {Object} - Memory usage information
   */
  getMemoryUsage() {
    if (performance.memory) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
        usedMB: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        totalMB: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      usedMB: 0,
      totalMB: 0,
      note: 'Memory API not available'
    };
  }

  /**
   * Gets memory usage trend
   * @param {number} count - Number of recent measurements to analyze
   * @returns {Object} - Trend analysis
   */
  getTrend(count = 10) {
    const recent = this.measurements.slice(-count);
    if (recent.length < 2) return null;
    
    const first = recent[0];
    const last = recent[recent.length - 1];
    const change = last.usedJSHeapSize - first.usedJSHeapSize;
    const changePercent = (change / first.usedJSHeapSize) * 100;
    
    return {
      change,
      changePercent,
      trend: change > 0 ? 'increasing' : change < 0 ? 'decreasing' : 'stable',
      measurements: recent.length
    };
  }

  /**
   * Gets all measurements
   * @returns {Array} - Array of measurements
   */
  getMeasurements() {
    return [...this.measurements];
  }

  /**
   * Clears all measurements
   */
  clear() {
    this.measurements = [];
  }
}

/**
 * Frame rate monitoring
 */
export class FrameRateMonitor {
  constructor() {
    this.frames = [];
    this.isRunning = false;
    this.animationId = null;
  }

  /**
   * Starts monitoring frame rate
   */
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.frames = [];
    this.measureFrame();
  }

  /**
   * Stops monitoring frame rate
   */
  stop() {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * Measures a single frame
   */
  measureFrame() {
    if (!this.isRunning) return;
    
    const now = performance.now();
    this.frames.push(now);
    
    // Keep only last 60 frames (1 second at 60fps)
    if (this.frames.length > 60) {
      this.frames.shift();
    }
    
    this.animationId = requestAnimationFrame(() => this.measureFrame());
  }

  /**
   * Gets current frame rate
   * @returns {number} - Frames per second
   */
  getFPS() {
    if (this.frames.length < 2) return 0;
    
    const timeSpan = this.frames[this.frames.length - 1] - this.frames[0];
    const fps = (this.frames.length - 1) / (timeSpan / 1000);
    
    return Math.round(fps);
  }

  /**
   * Gets frame rate statistics
   * @returns {Object} - Frame rate statistics
   */
  getStats() {
    const fps = this.getFPS();
    
    return {
      fps,
      frameCount: this.frames.length,
      isSmooth: fps >= 55, // Consider 55+ fps as smooth
      performance: fps >= 55 ? 'good' : fps >= 30 ? 'fair' : 'poor'
    };
  }
}

// Create singleton instances
export const memoryMonitor = new MemoryMonitor();
export const frameRateMonitor = new FrameRateMonitor();

/**
 * Debounced function wrapper for performance
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttled function wrapper for performance
 * @param {Function} func - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} - Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Lazy loading utility for components
 * @param {Function} importFunc - Dynamic import function
 * @returns {React.Component} - Lazy loaded component
 */
export const lazyLoad = (importFunc) => {
  return React.lazy(importFunc);
};
