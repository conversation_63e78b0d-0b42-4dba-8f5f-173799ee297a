import { VALIDATION_RULES } from '../config/constants';

/**
 * Validates a file name according to application rules
 * @param {string} fileName - The file name to validate
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateFileName = (fileName) => {
  const errors = [];
  
  if (!fileName || typeof fileName !== 'string') {
    errors.push('File name is required');
    return { isValid: false, errors };
  }
  
  const trimmed = fileName.trim();
  
  if (trimmed.length < VALIDATION_RULES.fileName.minLength) {
    errors.push('File name is too short');
  }
  
  if (trimmed.length > VALIDATION_RULES.fileName.maxLength) {
    errors.push('File name is too long');
  }
  
  if (!VALIDATION_RULES.fileName.allowedChars.test(trimmed)) {
    errors.push('File name contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitized: trimmed
  };
};

/**
 * Validates project name
 * @param {string} projectName - The project name to validate
 * @returns {Object} - Validation result
 */
export const validateProjectName = (projectName) => {
  const errors = [];
  
  if (!projectName || typeof projectName !== 'string') {
    errors.push('Project name is required');
    return { isValid: false, errors };
  }
  
  const trimmed = projectName.trim();
  
  if (trimmed.length < VALIDATION_RULES.projectName.minLength) {
    errors.push('Project name is too short');
  }
  
  if (trimmed.length > VALIDATION_RULES.projectName.maxLength) {
    errors.push('Project name is too long');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitized: trimmed
  };
};

/**
 * Validates task name
 * @param {string} taskName - The task name to validate
 * @returns {Object} - Validation result
 */
export const validateTaskName = (taskName) => {
  const errors = [];
  
  if (!taskName || typeof taskName !== 'string') {
    errors.push('Task name is required');
    return { isValid: false, errors };
  }
  
  const trimmed = taskName.trim();
  
  if (trimmed.length < VALIDATION_RULES.taskName.minLength) {
    errors.push('Task name is too short');
  }
  
  if (trimmed.length > VALIDATION_RULES.taskName.maxLength) {
    errors.push('Task name is too long');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitized: trimmed
  };
};

/**
 * Validates date string
 * @param {string} dateString - The date string to validate
 * @returns {Object} - Validation result
 */
export const validateDate = (dateString) => {
  const errors = [];
  
  if (!dateString) {
    return { isValid: true, errors: [], sanitized: '' }; // Empty dates are allowed
  }
  
  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    errors.push('Invalid date format');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitized: dateString
  };
};

/**
 * Validates percentage value
 * @param {string|number} percent - The percentage to validate
 * @returns {Object} - Validation result
 */
export const validatePercent = (percent) => {
  const errors = [];
  const numPercent = Number(percent);
  
  if (isNaN(numPercent)) {
    errors.push('Percentage must be a number');
  } else if (numPercent < 0 || numPercent > 100) {
    errors.push('Percentage must be between 0 and 100');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitized: Math.max(0, Math.min(100, numPercent))
  };
};
