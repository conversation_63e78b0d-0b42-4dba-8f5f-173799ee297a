import { colors, spacing, typography, shadows, borderRadius, components } from '../styles/theme';

/**
 * Utility function to merge styles
 * @param {...Object} styles - Style objects to merge
 * @returns {Object} - Merged style object
 */
export const mergeStyles = (...styles) => {
  return Object.assign({}, ...styles);
};

/**
 * Creates a button style based on variant
 * @param {string} variant - Button variant (primary, secondary, error, outline)
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Button style object
 */
export const createButtonStyle = (variant = 'primary', customStyles = {}) => {
  const baseStyle = components.button.base;
  const variantStyle = components.button[variant] || components.button.primary;
  
  return mergeStyles(baseStyle, variantStyle, customStyles);
};

/**
 * Creates a card style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Card style object
 */
export const createCardStyle = (customStyles = {}) => {
  return mergeStyles(components.card.base, customStyles);
};

/**
 * Creates an input style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Input style object
 */
export const createInputStyle = (customStyles = {}) => {
  return mergeStyles(components.input.base, customStyles);
};

/**
 * Creates a textarea style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Textarea style object
 */
export const createTextareaStyle = (customStyles = {}) => {
  return mergeStyles(components.textarea.base, customStyles);
};

/**
 * Creates an error message style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Error message style object
 */
export const createErrorStyle = (customStyles = {}) => {
  return mergeStyles({
    backgroundColor: colors.error.background,
    color: colors.error.dark,
    padding: spacing.md,
    borderRadius: borderRadius.base,
    marginBottom: spacing.md,
    border: `1px solid ${colors.error.light}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  }, customStyles);
};

/**
 * Creates a success message style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Success message style object
 */
export const createSuccessStyle = (customStyles = {}) => {
  return mergeStyles({
    backgroundColor: colors.success.background,
    color: colors.success.dark,
    padding: spacing.md,
    borderRadius: borderRadius.base,
    marginBottom: spacing.md,
    border: `1px solid ${colors.success.light}`,
  }, customStyles);
};

/**
 * Creates a container style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Container style object
 */
export const createContainerStyle = (customStyles = {}) => {
  return mergeStyles({
    maxWidth: '1000px',
    margin: `${spacing.xl} auto`,
    fontFamily: typography.fontFamily,
    padding: `0 ${spacing.md}`,
  }, customStyles);
};

/**
 * Creates a flex layout style
 * @param {string} direction - Flex direction (row, column)
 * @param {string} justify - Justify content
 * @param {string} align - Align items
 * @param {string} gap - Gap between items
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Flex style object
 */
export const createFlexStyle = (
  direction = 'row',
  justify = 'flex-start',
  align = 'stretch',
  gap = spacing.sm,
  customStyles = {}
) => {
  return mergeStyles({
    display: 'flex',
    flexDirection: direction,
    justifyContent: justify,
    alignItems: align,
    gap,
  }, customStyles);
};

/**
 * Creates a grid layout style
 * @param {string} columns - Grid template columns
 * @param {string} gap - Gap between grid items
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Grid style object
 */
export const createGridStyle = (columns = '1fr', gap = spacing.md, customStyles = {}) => {
  return mergeStyles({
    display: 'grid',
    gridTemplateColumns: columns,
    gap,
  }, customStyles);
};

/**
 * Creates a responsive style based on screen size
 * @param {Object} styles - Object with breakpoint keys and style values
 * @returns {Object} - Responsive style object
 */
export const createResponsiveStyle = (styles) => {
  // This is a simplified version - in a real app you might use CSS-in-JS libraries
  // that support media queries better
  return styles.base || {};
};

/**
 * Creates a loading spinner style
 * @param {string} size - Size of the spinner (sm, md, lg)
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Spinner style object
 */
export const createSpinnerStyle = (size = 'md', customStyles = {}) => {
  const sizes = {
    sm: '16px',
    md: '24px',
    lg: '32px',
  };
  
  return mergeStyles({
    width: sizes[size],
    height: sizes[size],
    border: `2px solid ${colors.grey[300]}`,
    borderTop: `2px solid ${colors.primary.main}`,
    borderRadius: borderRadius.full,
    animation: 'spin 1s linear infinite',
  }, customStyles);
};

/**
 * Creates a modal overlay style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Modal overlay style object
 */
export const createModalOverlayStyle = (customStyles = {}) => {
  return mergeStyles({
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  }, customStyles);
};

/**
 * Creates a modal content style
 * @param {Object} customStyles - Additional custom styles
 * @returns {Object} - Modal content style object
 */
export const createModalContentStyle = (customStyles = {}) => {
  return mergeStyles({
    backgroundColor: colors.background.paper,
    borderRadius: borderRadius.lg,
    boxShadow: shadows.xl,
    padding: spacing.xl,
    maxWidth: '90vw',
    maxHeight: '90vh',
    overflow: 'auto',
  }, customStyles);
};
