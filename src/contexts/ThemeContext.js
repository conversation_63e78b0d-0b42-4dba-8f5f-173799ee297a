import React, { createContext, useContext, useState, useEffect } from 'react';
import { lightTheme, darkTheme, createComponents } from '../styles/theme';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  // Initialize theme from localStorage or default to light
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('darkMode');
    return saved ? JSON.parse(saved) : false;
  });

  // Update localStorage when theme changes
  useEffect(() => {
    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  // Apply theme to document root for CSS custom properties
  useEffect(() => {
    const theme = isDarkMode ? darkTheme : lightTheme;
    const root = document.documentElement;
    
    // Set CSS custom properties for theme colors
    root.style.setProperty('--color-background-default', theme.background.default);
    root.style.setProperty('--color-background-paper', theme.background.paper);
    root.style.setProperty('--color-background-elevated', theme.background.elevated);
    root.style.setProperty('--color-background-hover', theme.background.hover);
    root.style.setProperty('--color-text-primary', theme.text.primary);
    root.style.setProperty('--color-text-secondary', theme.text.secondary);
    root.style.setProperty('--color-border-light', theme.border.light);
    root.style.setProperty('--color-border-main', theme.border.main);
    root.style.setProperty('--color-primary-main', theme.primary.main);
    root.style.setProperty('--color-secondary-main', theme.secondary.main);
    root.style.setProperty('--color-error-main', theme.error.main);
    root.style.setProperty('--color-success-main', theme.success.main);
    root.style.setProperty('--color-warning-main', theme.warning.main);
    
    // Update body background
    document.body.style.backgroundColor = theme.background.default;
    document.body.style.color = theme.text.primary;
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  const theme = isDarkMode ? darkTheme : lightTheme;
  const components = createComponents(theme);

  const value = {
    theme,
    components,
    isDarkMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
