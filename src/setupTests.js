// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock Plotly.js for Jest environment
jest.mock('plotly.js-dist-min', () => ({
  __esModule: true,
  default: {
    newPlot: jest.fn(() => Promise.resolve()),
    react: jest.fn(() => Promise.resolve()),
    relayout: jest.fn(() => Promise.resolve()),
    restyle: jest.fn(() => Promise.resolve()),
    redraw: jest.fn(() => Promise.resolve()),
    purge: jest.fn(() => Promise.resolve()),
  },
  newPlot: jest.fn(() => Promise.resolve()),
  react: jest.fn(() => Promise.resolve()),
  relayout: jest.fn(() => Promise.resolve()),
  restyle: jest.fn(() => Promise.resolve()),
  redraw: jest.fn(() => Promise.resolve()),
  purge: jest.fn(() => Promise.resolve()),
}));

// Mock File System Access API for tests
Object.defineProperty(window, 'showDirectoryPicker', {
  writable: true,
  value: jest.fn(),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
