// Mock implementation of Plotly.js for Jest tests
const mockPlotly = {
  newPlot: jest.fn(() => Promise.resolve()),
  react: jest.fn(() => Promise.resolve()),
  relayout: jest.fn(() => Promise.resolve()),
  restyle: jest.fn(() => Promise.resolve()),
  redraw: jest.fn(() => Promise.resolve()),
  purge: jest.fn(() => Promise.resolve()),
  plot: jest.fn(() => Promise.resolve()),
  Plots: {
    resize: jest.fn(),
  },
};

export default mockPlotly;
