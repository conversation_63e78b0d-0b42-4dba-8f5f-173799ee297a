import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius, shadows } from '../styles/theme';

const Toast = ({ message, type = 'info', duration = 3000, onClose }) => {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // Wait for fade out animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: theme.success.background,
          color: theme.success.dark,
          borderColor: theme.success.main,
        };
      case 'error':
        return {
          backgroundColor: theme.error.background,
          color: theme.error.dark,
          borderColor: theme.error.main,
        };
      case 'warning':
        return {
          backgroundColor: theme.warning.background,
          color: theme.warning.dark,
          borderColor: theme.warning.main,
        };
      default:
        return {
          backgroundColor: theme.background.elevated,
          color: theme.text.primary,
          borderColor: theme.border.main,
        };
    }
  };

  const typeStyles = getTypeStyles();

  const toastStyle = {
    position: 'fixed',
    top: spacing.lg,
    right: spacing.lg,
    padding: spacing.md,
    borderRadius: borderRadius.base,
    boxShadow: shadows.lg,
    border: `1px solid ${typeStyles.borderColor}`,
    backgroundColor: typeStyles.backgroundColor,
    color: typeStyles.color,
    maxWidth: '400px',
    zIndex: 1000,
    opacity: isVisible ? 1 : 0,
    transform: `translateX(${isVisible ? '0' : '100%'})`,
    transition: 'all 0.3s ease-in-out',
    display: 'flex',
    alignItems: 'center',
    gap: spacing.sm,
  };

  const closeButtonStyle = {
    background: 'none',
    border: 'none',
    color: typeStyles.color,
    cursor: 'pointer',
    fontSize: '1.2rem',
    marginLeft: 'auto',
    padding: '0',
    opacity: 0.7,
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      default:
        return 'ℹ️';
    }
  };

  return (
    <div style={toastStyle}>
      <span>{getIcon()}</span>
      <span style={{ flex: 1 }}>{message}</span>
      <button
        onClick={() => {
          setIsVisible(false);
          setTimeout(onClose, 300);
        }}
        style={closeButtonStyle}
        onMouseEnter={(e) => e.target.style.opacity = '1'}
        onMouseLeave={(e) => e.target.style.opacity = '0.7'}
      >
        ×
      </button>
    </div>
  );
};

// Toast container component to manage multiple toasts
export const ToastContainer = ({ toasts, removeToast }) => {
  return (
    <>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </>
  );
};

export default Toast;
