import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius, shadows } from '../styles/theme';

const DailyNoteEditor = ({ initialContent, onSave, onCancel }) => {
  const [content, setContent] = useState(initialContent);
  const [showLivePreview, setShowLivePreview] = useState(true);
  const { theme, components } = useTheme();

  const handleTogglePreview = () => {
    setShowLivePreview(prev => !prev);
  };

  const containerStyle = {
    backgroundColor: theme.background.paper,
    border: `1px solid ${theme.border.light}`,
    borderRadius: borderRadius.base,
    padding: spacing.sm,
    boxShadow: shadows.base,
  };

  const headerStyle = {
    marginBottom: spacing.sm,
    textAlign: 'right',
  };

  const toggleButtonStyle = {
    ...components.button.base,
    ...components.button.ghost,
    fontSize: '0.875rem',
  };

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>
        <button onClick={handleTogglePreview} style={toggleButtonStyle}>
          {showLivePreview ? 'Hide Live Preview' : 'Show Live Preview'}
        </button>
      </div>
      {showLivePreview ? (
        <div style={{ display: 'flex', gap: spacing.lg }}>
          {/* Preview panel comes first */}
          <div
            style={{
              width: '50%',
              minHeight: '300px',
              padding: spacing.sm,
              backgroundColor: theme.background.elevated,
              border: `1px solid ${theme.border.light}`,
              borderRadius: borderRadius.base,
              overflowY: 'auto',
              color: theme.text.primary,
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
            }}
          >
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {content}
            </ReactMarkdown>
          </div>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            style={{
              ...components.textarea.base,
              width: '50%',
              minHeight: '300px',
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
            }}
          />
        </div>
      ) : (
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          style={{
            ...components.textarea.base,
            width: '100%',
            minHeight: '300px',
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
          }}
        />
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: spacing.sm,
          gap: spacing.sm,
        }}
      >
        <button
          onClick={onCancel}
          style={{
            ...components.button.base,
            ...components.button.error,
          }}
        >
          Cancel
        </button>
        <button
          onClick={() => onSave(content)}
          style={{
            ...components.button.base,
            ...components.button.secondary,
          }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default DailyNoteEditor;
