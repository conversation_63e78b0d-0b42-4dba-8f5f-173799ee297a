// components/GoalsEditor.js
import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius, shadows } from '../styles/theme';

const GoalsEditor = ({ initialContent, onSave, onCancel }) => {
  const { theme, components } = useTheme();
  const [projects, setProjects] = useState([]);

  // Parse initial content when component mounts
  useEffect(() => {
    parseGoalsContent(initialContent);

    // Clean up any empty tasks that might have persisted
    setTimeout(() => {
      setProjects(prevProjects => {
        const cleanedProjects = prevProjects.map(project => {
          // Filter out completely empty tasks
          const cleanedTasks = project.tasks.filter(task => {
            return task.task.trim() || task.start || task.finish || task.percent !== '0';
          });
          return { ...project, tasks: cleanedTasks };
        });
        return cleanedProjects;
      });
    }, 0);
  }, [initialContent]);

  const parseGoalsContent = (content) => {
    try {
      if (!content || typeof content !== 'string') {
        console.warn('Invalid content provided to parseGoalsContent');
        setProjects([]);
        return;
      }
      
      const lines = content.split('\n');
      const parsedProjects = [];
      let currentProject = null;
      let inDataSection = false;

      lines.forEach((line, index) => {
        try {
          const trimmedLine = line.trim();

          // Check if we're entering the DATA section
          if (trimmedLine === '# DATA') {
            inDataSection = true;
            return;
          }

          // Only process content within the DATA section
          if (!inDataSection) return;

          // Stop processing if we hit another main section
          if (trimmedLine.startsWith('# ') && trimmedLine !== '# DATA') {
            inDataSection = false;
            return;
          }

          if (trimmedLine.startsWith('## ')) {
            // New project - save previous project if exists
            if (currentProject) {
              // Filter out any empty tasks before adding project
              currentProject.tasks = currentProject.tasks.filter(task => task.task && task.task.trim());
              parsedProjects.push(currentProject);
            }
            currentProject = {
              name: trimmedLine.slice(3).trim(),
              color: '',
              tasks: []
            };
          } else if (trimmedLine.startsWith('- ProjectColor:') && currentProject) {
            currentProject.color = trimmedLine.split(':')[1].trim();
          } else if (trimmedLine.startsWith('| ') && currentProject) {
            // Skip table header and separator rows
            if (trimmedLine.includes('| Done | Task |') ||
                trimmedLine.includes('| ---- |') ||
                trimmedLine === '|') {
              return;
            }

            // Parse task row
            const columns = line.split('|').map(col => col.trim());
            if (columns.length >= 6) {
              const taskText = columns[2] || '';

              // Check if task is a subtask (starts with dash)
              const isSubtask = taskText.startsWith('- ');
              const cleanTaskText = isSubtask ? taskText.substring(2) : taskText;

              const task = {
                done: columns[1] || '[ ]',
                task: cleanTaskText,
                start: columns[3] || '',
                finish: columns[4] || '',
                percent: columns[5] || '0',
                isSubtask: isSubtask
              };

              // Only add non-empty tasks
              if (task.task.trim()) {
                currentProject.tasks.push(task);
              }
            }
          }
        } catch (lineError) {
          console.error(`Error parsing line ${index}:`, lineError);
        }
      });

      // Add last project
      if (currentProject) {
        // Filter out any empty tasks before adding project
        currentProject.tasks = currentProject.tasks.filter(task => task.task && task.task.trim());
        parsedProjects.push(currentProject);
      }

      setProjects(parsedProjects);
    } catch (error) {
      console.error('Error parsing goals content:', error);
      setProjects([]); // Set empty projects on error
    }
  };

  const generateMarkdownContent = () => {
    let content = '# DATA\n\n';

    content += projects.map(project => {
      let projectContent = `## ${project.name}\n`;
      projectContent += `- ProjectColor: ${project.color}\n\n`;
      projectContent += `| Done | Task | Start | Finish | Percent |\n`;
      projectContent += `| ---- | ---- | ----- | ------ | ------- |\n`;

      // Filter out empty tasks and format with subtask prefix if needed
      project.tasks
        .filter(task => task.task && task.task.trim()) // Only include tasks with content
        .forEach(task => {
          const done = task.done || '[ ]';
          const taskName = task.isSubtask ? `- ${task.task}` : task.task;
          const start = task.start || '';
          const finish = task.finish || '';
          const percent = task.percent || '0';

          projectContent += `| ${done} | ${taskName} | ${start} | ${finish} | ${percent} |\n`;
        });

      return projectContent + '\n';
    }).join('');

    return content;
  };

  const handleSave = () => {
    try {
      const markdownContent = generateMarkdownContent();
      
      // Basic validation to ensure we have meaningful content
      if (!markdownContent || markdownContent.trim().length < 20) {
        console.warn('Empty or minimal content detected');
        // Still allow saving but with a warning
      }
      
      onSave(markdownContent);
    } catch (error) {
      console.error('Error generating markdown content:', error);
      // You might want to show an error message to the user here
    }
  };

  const addProject = () => {
    setProjects([
      ...projects, 
      { 
        name: 'New Project', 
        color: 'gray', 
        tasks: [] 
      }
    ]);
  };

  const addTask = (projectIndex) => {
    try {
      const newProjects = [...projects];
      if (!newProjects[projectIndex]) {
        console.error('Invalid project index:', projectIndex);
        return;
      }

      // Don't add if the last task is already empty
      const projectTasks = newProjects[projectIndex].tasks;
      const lastTask = projectTasks[projectTasks.length - 1];
      if (lastTask &&
          !lastTask.task.trim() &&
          !lastTask.start &&
          !lastTask.finish &&
          lastTask.percent === '0') {
        console.log('Skipping add task - last task is already empty');
        return;
      }

      newProjects[projectIndex].tasks.push({
        done: '[ ]',
        task: '',
        start: '',
        finish: '',
        percent: '0',
        isSubtask: false
      });
      setProjects(newProjects);
    } catch (error) {
      console.error('Error adding task:', error);
    }
  };

  // Add a function to add a subtask directly after a specific task
  const addSubtask = (projectIndex, taskIndex) => {
    try {
      const newProjects = [...projects];
      if (!newProjects[projectIndex] || !newProjects[projectIndex].tasks[taskIndex]) {
        console.error('Invalid project or task index:', projectIndex, taskIndex);
        return;
      }

      const parentTask = newProjects[projectIndex].tasks[taskIndex];

      // Add the new subtask right after the selected task
      const insertPosition = taskIndex + 1;

      newProjects[projectIndex].tasks.splice(insertPosition, 0, {
        done: '[ ]',
        task: '',
        start: parentTask.start || '', // Inherit start date from parent
        finish: parentTask.finish || '', // Inherit finish date from parent
        percent: '0',
        isSubtask: true
      });

      setProjects(newProjects);
    } catch (error) {
      console.error('Error adding subtask:', error);
    }
  };

  const updateProject = (projectIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex][field] = value;
    setProjects(newProjects);
  };

  const updateTask = (projectIndex, taskIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks[taskIndex][field] = value;
    setProjects(newProjects);
  };

  // Add a new function to convert a task to/from subtask
  const toggleSubtask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    const task = newProjects[projectIndex].tasks[taskIndex];
    task.isSubtask = !task.isSubtask;
    setProjects(newProjects);
  };

  const deleteProject = (projectIndex) => {
    const newProjects = projects.filter((_, index) => index !== projectIndex);
    setProjects(newProjects);
  };

  const deleteTask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks = newProjects[projectIndex].tasks.filter((_, index) => index !== taskIndex);
    setProjects(newProjects);
  };

  const containerStyle = {
    padding: spacing.lg,
    backgroundColor: theme.background.default,
    borderRadius: borderRadius.base,
    boxShadow: shadows.medium,
    marginBottom: spacing.lg,
  };

  const headerStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  };

  const titleStyle = {
    margin: 0,
    color: theme.text.primary,
    fontSize: '1.5rem',
    fontWeight: '600',
  };

  const buttonGroupStyle = {
    display: 'flex',
    gap: spacing.sm,
  };

  const projectHeaderStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.md,
    marginBottom: spacing.sm,
    padding: spacing.sm,
    backgroundColor: theme.background.paper,
    borderRadius: borderRadius.base,
    border: `1px solid ${theme.border.light}`,
  };

  const inputStyle = {
    ...components.input.base,
    width: '100%',
    padding: spacing.xs,
    border: `1px solid ${theme.border.light}`,
    borderRadius: borderRadius.small,
  };

  const tableStyle = {
    width: '100%',
    borderCollapse: 'collapse',
    marginBottom: spacing.md,
    backgroundColor: theme.background.paper,
    borderRadius: borderRadius.base,
    border: `1px solid ${theme.border.light}`,
  };

  const cellStyle = {
    padding: spacing.xs,
    border: `1px solid ${theme.border.light}`,
  };

  const headerCellStyle = {
    ...cellStyle,
    backgroundColor: theme.background.alt,
    fontWeight: 'bold',
  };

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>
        <h2 style={titleStyle}>Edit Goals</h2>
        <div style={buttonGroupStyle}>
          <button
            onClick={addProject}
            style={{
              ...components.button.base,
              ...components.button.primary,
            }}
          >
            Add Project
          </button>
          <button
            onClick={handleSave}
            style={{
              ...components.button.base,
              ...components.button.success,
            }}
          >
            Save Goals
          </button>
          <button
            onClick={onCancel}
            style={{
              ...components.button.base,
              ...components.button.secondary,
            }}
          >
            Cancel
          </button>
        </div>
      </div>

      {projects.map((project, projectIndex) => {
        return (
          <div key={projectIndex} style={{ marginBottom: spacing.lg }}>
            <div style={projectHeaderStyle}>
              <input
                type="text"
                value={project.name}
                onChange={(e) => updateProject(projectIndex, 'name', e.target.value)}
                placeholder="Project Name"
                style={{
                  ...inputStyle,
                  fontWeight: 'bold',
                  fontSize: '1.1rem',
                  flex: 1,
                }}
              />
              <input
                type="text"
                value={project.color}
                onChange={(e) => updateProject(projectIndex, 'color', e.target.value)}
                placeholder="Color (e.g. blue)"
                style={{
                  ...inputStyle,
                  width: '120px',
                }}
              />
              <button
                onClick={() => deleteProject(projectIndex)}
                style={{
                  ...components.button.base,
                  ...components.button.error,
                  padding: '5px 10px',
                }}
              >
                Delete Project
              </button>
            </div>

            <table style={tableStyle}>
              <thead>
                <tr>
                  <th style={headerCellStyle}>Done</th>
                  <th style={{ ...headerCellStyle, width: '40%' }}>Task</th>
                  <th style={headerCellStyle}>Start</th>
                  <th style={headerCellStyle}>Finish</th>
                  <th style={headerCellStyle}>Percent</th>
                  <th style={headerCellStyle}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {project.tasks.map((task, taskIndex) => {
                  return (
                    <tr key={taskIndex}>
                      <td style={cellStyle}>
                        <select
                          value={task.done}
                          onChange={(e) => updateTask(projectIndex, taskIndex, 'done', e.target.value)}
                          style={inputStyle}
                        >
                          <option value="[ ]">[ ] Not Started</option>
                          <option value="[x]">[x] Complete</option>
                        </select>
                      </td>
                      <td style={cellStyle}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          {task.isSubtask && (
                            <span style={{ marginRight: spacing.xs, color: theme.text.muted }}>↳</span>
                          )}
                          <input
                            type="text"
                            value={task.task}
                            onChange={(e) => updateTask(projectIndex, taskIndex, 'task', e.target.value)}
                            style={inputStyle}
                            placeholder={task.isSubtask ? "Enter subtask..." : "Enter main task..."}
                          />
                        </div>
                      </td>
                      <td style={cellStyle}>
                        <input
                          type="date"
                          value={task.start}
                          onChange={(e) => updateTask(projectIndex, taskIndex, 'start', e.target.value)}
                          style={inputStyle}
                        />
                      </td>
                      <td style={cellStyle}>
                        <input
                          type="date"
                          value={task.finish}
                          onChange={(e) => updateTask(projectIndex, taskIndex, 'finish', e.target.value)}
                          style={inputStyle}
                        />
                      </td>
                      <td style={cellStyle}>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={task.percent}
                          onChange={(e) => updateTask(projectIndex, taskIndex, 'percent', e.target.value)}
                          style={{ ...inputStyle, width: '60px' }}
                        />
                      </td>
                      <td style={{ ...cellStyle, textAlign: 'center' }}>
                        <button
                          onClick={() => deleteTask(projectIndex, taskIndex)}
                          style={{
                            ...components.button.base,
                            ...components.button.error,
                            padding: '3px 6px',
                            fontSize: '0.8rem',
                          }}
                        >
                          Delete
                        </button>
                        <button
                          onClick={() => addSubtask(projectIndex, taskIndex)}
                          style={{
                            ...components.button.base,
                            ...components.button.secondary,
                            padding: '3px 6px',
                            fontSize: '0.8rem',
                            marginLeft: '5px',
                          }}
                          title="Add subtask below this task"
                        >
                          ↳+
                        </button>
                        <button
                          onClick={() => toggleSubtask(projectIndex, taskIndex)}
                          style={{
                            ...components.button.base,
                            ...components.button.secondary,
                            padding: '3px 6px',
                            fontSize: '0.8rem',
                            marginLeft: '5px',
                          }}
                          title={task.isSubtask ? "Convert to main task" : "Convert to subtask"}
                        >
                          {task.isSubtask ? "↥" : "↧"}
                        </button>
                      </td>
                    </tr>
                  );
                })}
            </tbody>
            </table>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: spacing.sm,
              gap: spacing.sm
            }}>
              <button
                onClick={() => addTask(projectIndex)}
                style={{
                  ...components.button.base,
                  ...components.button.secondary,
                  flex: 1
                }}
              >
                Add Main Task
              </button>
              <button
                onClick={() => {
                  const newProjects = [...projects];
                  newProjects[projectIndex].tasks.push({
                    done: '[ ]',
                    task: '',
                    start: '',
                    finish: '',
                    percent: '0',
                    isSubtask: true
                  });
                  setProjects(newProjects);
                }}
                style={{
                  ...components.button.base,
                  ...components.button.secondary,
                  flex: 1
                }}
              >
                Add Subtask
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default GoalsEditor;