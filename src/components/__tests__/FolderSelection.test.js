import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import FolderSelection from '../FolderSelection';

describe('FolderSelection Component', () => {
  const defaultProps = {
    supportsDirectoryPicker: true,
    DEFAULT_FOLDER_NAME: 'test-folder',
    handleSelectFolder: jest.fn(),
    fileInputRef: { current: null },
    handleFallbackFiles: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders welcome message', () => {
    render(<FolderSelection {...defaultProps} />);
    
    expect(screen.getByText(/Welcome! It seems like you haven't selected your daily notes folder yet/)).toBeInTheDocument();
  });

  test('renders folder name when directory picker is supported', () => {
    render(<FolderSelection {...defaultProps} />);
    
    expect(screen.getByText(/test-folder/)).toBeInTheDocument();
    expect(screen.getByText(/Please select the/)).toBeInTheDocument();
  });

  test('renders fallback message when directory picker is not supported', () => {
    const props = {
      ...defaultProps,
      supportsDirectoryPicker: false,
    };
    
    render(<FolderSelection {...props} />);
    
    expect(screen.getByText(/Your browser doesn't support the native folder picker/)).toBeInTheDocument();
  });

  test('renders select folder button', () => {
    render(<FolderSelection {...defaultProps} />);
    
    const button = screen.getByText('Select Daily Notes Folder');
    expect(button).toBeInTheDocument();
    expect(button.tagName).toBe('BUTTON');
  });

  test('calls handleSelectFolder when button is clicked', () => {
    render(<FolderSelection {...defaultProps} />);
    
    const button = screen.getByText('Select Daily Notes Folder');
    fireEvent.click(button);
    
    expect(defaultProps.handleSelectFolder).toHaveBeenCalledTimes(1);
  });

  test('renders hidden file input', () => {
    const fileInputRef = { current: document.createElement('input') };
    const props = {
      ...defaultProps,
      fileInputRef,
    };
    
    render(<FolderSelection {...props} />);
    
    const fileInput = screen.getByDisplayValue('');
    expect(fileInput).toBeInTheDocument();
    expect(fileInput.type).toBe('file');
    expect(fileInput.style.display).toBe('none');
  });

  test('file input has correct attributes', () => {
    const fileInputRef = { current: document.createElement('input') };
    const props = {
      ...defaultProps,
      fileInputRef,
    };
    
    render(<FolderSelection {...props} />);
    
    const fileInput = screen.getByDisplayValue('');
    expect(fileInput).toHaveAttribute('webkitdirectory', 'true');
    expect(fileInput).toHaveAttribute('directory', 'true');
    expect(fileInput).toHaveAttribute('multiple');
  });

  test('calls handleFallbackFiles when file input changes', () => {
    const fileInputRef = { current: document.createElement('input') };
    const props = {
      ...defaultProps,
      fileInputRef,
    };

    render(<FolderSelection {...props} />);

    const fileInput = screen.getByDisplayValue('');
    const mockFiles = [new File(['content'], 'test.md', { type: 'text/markdown' })];

    // Create a proper file input change event
    Object.defineProperty(fileInput, 'files', {
      value: mockFiles,
      writable: false,
    });

    fireEvent.change(fileInput);

    expect(props.handleFallbackFiles).toHaveBeenCalledTimes(1);
    // Just check that it was called, not the exact event object
  });

  test('applies correct styling', () => {
    render(<FolderSelection {...defaultProps} />);
    
    const section = screen.getByText(/Welcome! It seems like you haven't selected your daily notes folder yet/).closest('section');
    expect(section).toHaveStyle('padding: 1.5rem');
    expect(section).toHaveStyle('margin-bottom: 1.5rem');
  });

  test('button has correct styling', () => {
    render(<FolderSelection {...defaultProps} />);

    const button = screen.getByText('Select Daily Notes Folder');
    expect(button).toHaveStyle('cursor: pointer');
    // Note: border style may vary between browsers, so we just check cursor
  });
});
