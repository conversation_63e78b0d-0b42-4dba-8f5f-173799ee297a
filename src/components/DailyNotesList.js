// DailyNotesList.js
import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius, shadows } from '../styles/theme';

const DailyNotesList = ({ notes, selectedNote, onSelectNote }) => {
  const { theme } = useTheme();
  // newest-first
  const sortedNotes = [...notes].sort((a, b) => b.localeCompare(a));

  const containerStyle = {
    width: window.innerWidth < 768 ? '100%' : '200px',
    flexShrink: 0,
    backgroundColor: theme.background.paper,
    border: `1px solid ${theme.border.light}`,
    borderRadius: borderRadius.base,
    padding: spacing.sm,
    maxHeight: window.innerWidth < 768 ? '200px' : '70vh',
    overflowY: 'auto',
    boxShadow: shadows.sm,
    marginBottom: window.innerWidth < 768 ? spacing.md : 0,
  };

  const titleStyle = {
    margin: `0 0 ${spacing.sm} 0`,
    fontSize: '1rem',
    fontWeight: '600',
    color: theme.text.primary,
  };

  const listStyle = {
    listStyle: 'none',
    padding: 0,
    margin: 0,
  };

  const emptyItemStyle = {
    padding: spacing.sm,
    color: theme.text.secondary,
    fontStyle: 'italic',
  };

  const getItemStyle = (isSelected) => ({
    padding: spacing.sm,
    cursor: 'pointer',
    backgroundColor: isSelected ? theme.background.hover : 'transparent',
    borderRadius: borderRadius.sm,
    marginBottom: spacing.xs,
    transition: 'background-color 0.2s ease',
    color: theme.text.primary,
    ':hover': {
      backgroundColor: theme.background.hover,
    },
  });

  return (
    <div style={containerStyle}>
      <h3 style={titleStyle}>Daily Notes</h3>
      <ul style={listStyle}>
        {sortedNotes.length === 0 ? (
          <li style={emptyItemStyle}>No notes found</li>
        ) : (
          sortedNotes.map((note) => (
            <li
              key={note}
              onClick={() => onSelectNote(note)}
              style={getItemStyle(note === selectedNote)}
              onMouseEnter={(e) => {
                if (note !== selectedNote) {
                  e.target.style.backgroundColor = theme.background.hover;
                }
              }}
              onMouseLeave={(e) => {
                if (note !== selectedNote) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
            >
              {note === new Date().toISOString().slice(0, 10) ? (
                <strong>{note} (Today)</strong>
              ) : (
                note
              )}
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default DailyNotesList;
