import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius } from '../styles/theme';

const ThemeToggle = ({ className = '', style = {} }) => {
  const { isDarkMode, toggleTheme, theme } = useTheme();

  const toggleStyle = {
    position: 'relative',
    display: 'inline-flex',
    alignItems: 'center',
    gap: spacing.sm,
    padding: spacing.xs,
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    borderRadius: borderRadius.base,
    transition: 'all 0.2s ease-in-out',
    color: theme.text.primary,
    fontSize: '14px',
    fontWeight: '500',
    ...style,
  };

  const switchStyle = {
    position: 'relative',
    width: '48px',
    height: '24px',
    backgroundColor: isDarkMode ? theme.primary.main : theme.border.main,
    borderRadius: '12px',
    transition: 'background-color 0.3s ease',
    cursor: 'pointer',
  };

  const knobStyle = {
    position: 'absolute',
    top: '2px',
    left: isDarkMode ? '26px' : '2px',
    width: '20px',
    height: '20px',
    backgroundColor: '#ffffff',
    borderRadius: '50%',
    transition: 'left 0.3s ease',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
  };

  const iconStyle = {
    fontSize: '16px',
    transition: 'opacity 0.2s ease',
  };

  return (
    <button
      onClick={toggleTheme}
      className={className}
      style={toggleStyle}
      aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
      title={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
    >
      <span style={{ ...iconStyle, opacity: isDarkMode ? 0.5 : 1 }}>
        ☀️
      </span>
      <div style={switchStyle}>
        <div style={knobStyle} />
      </div>
      <span style={{ ...iconStyle, opacity: isDarkMode ? 1 : 0.5 }}>
        🌙
      </span>
    </button>
  );
};

export default ThemeToggle;
