// components/FolderSelection.js
import React from 'react';
import { createCardStyle, createButtonStyle } from '../utils/styles';
import { spacing, typography } from '../styles/theme';

const FolderSelection = ({
  supportsDirectoryPicker,
  DEFAULT_FOLDER_NAME,
  handleSelectFolder,
  fileInputRef,
  handleFallbackFiles,
}) => {
  const cardStyle = createCardStyle();
  const buttonStyle = createButtonStyle('primary');

  const textStyle = {
    fontSize: typography.fontSize.base,
    lineHeight: typography.lineHeight.normal,
    marginBottom: spacing.lg,
    color: '#666',
  };

  return (
    <section style={cardStyle}>
      <p style={textStyle}>
        Welcome! It seems like you haven't selected your daily notes folder yet.
        {supportsDirectoryPicker ? (
          <>
            {' '}Please select the <strong>{DEFAULT_FOLDER_NAME}</strong> folder, or any other folder
            containing your notes.
          </>
        ) : (
          <>
            {' '}Your browser doesn't support the native folder picker. Please choose your daily notes folder
            using the button below.
          </>
        )}
      </p>
      <button
        onClick={handleSelectFolder}
        style={buttonStyle}
      >
        Select Daily Notes Folder
      </button>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        webkitdirectory="true"
        directory="true"
        multiple
        onChange={handleFallbackFiles}
      />
    </section>
  );
};

export default FolderSelection;
