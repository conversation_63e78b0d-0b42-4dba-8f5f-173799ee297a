import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

const LoadingSpinner = ({ size = 24, className = '', style = {} }) => {
  const { theme } = useTheme();

  const spinnerStyle = {
    width: `${size}px`,
    height: `${size}px`,
    border: `2px solid ${theme.border.light}`,
    borderTop: `2px solid ${theme.primary.main}`,
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    display: 'inline-block',
    ...style,
  };

  // Add keyframes for the spin animation
  React.useEffect(() => {
    const styleSheet = document.styleSheets[0];
    const keyframes = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    
    // Check if the keyframes already exist
    let ruleExists = false;
    for (let i = 0; i < styleSheet.cssRules.length; i++) {
      if (styleSheet.cssRules[i].name === 'spin') {
        ruleExists = true;
        break;
      }
    }
    
    if (!ruleExists) {
      styleSheet.insertRule(keyframes, styleSheet.cssRules.length);
    }
  }, []);

  return <div className={className} style={spinnerStyle} />;
};

export default LoadingSpinner;
