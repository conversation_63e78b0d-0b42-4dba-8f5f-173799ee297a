// components/DailyNotesSection.js
import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import DailyNotesList from './DailyNotesList';
import DailyNoteEditor from './DailyNoteEditor';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius, shadows } from '../styles/theme';

const DailyNotesSection = ({
  dailyMd,
  dailyNoteNames,
  selectedNote,
  isEditingNote,
  onSelectNote,
  onCreateNote,
  onEditNote,
  onSaveNote,
}) => {
  const { theme, components } = useTheme();

  const sectionStyle = {
    marginBottom: spacing.xl,
  };

  const headerStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  };

  const titleStyle = {
    margin: 0,
    color: theme.text.primary,
    fontSize: '1.5rem',
    fontWeight: '600',
  };

  const buttonGroupStyle = {
    display: 'flex',
    gap: spacing.sm,
  };

  const createButtonStyle = {
    ...components.button.base,
    ...components.button.secondary,
  };

  const editButtonStyle = {
    ...components.button.base,
    ...components.button.primary,
  };

  return (
    <section style={sectionStyle}>
      <div style={headerStyle}>
        <h2 style={titleStyle}>Note ({selectedNote})</h2>
        <div style={buttonGroupStyle}>
          <button onClick={onCreateNote} style={createButtonStyle}>
            New Today's Note
          </button>
          {!isEditingNote && (
            <button onClick={onEditNote} style={editButtonStyle}>
              Edit Note
            </button>
          )}
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          gap: spacing.lg,
          alignItems: 'flex-start',
          flexDirection: window.innerWidth < 768 ? 'column' : 'row'
        }}
      >
        <DailyNotesList
          notes={dailyNoteNames}
          selectedNote={selectedNote}
          onSelectNote={onSelectNote}
        />
        <div style={{ flex: 1, minWidth: 0, width: '100%' }}>
          {isEditingNote ? (
            <DailyNoteEditor initialContent={dailyMd} onSave={onSaveNote} onCancel={() => {}} />
          ) : (
            <article
              style={{
                backgroundColor: theme.background.paper,
                border: `1px solid ${theme.border.light}`,
                padding: spacing.lg,
                borderRadius: borderRadius.base,
                boxShadow: shadows.base,
                minHeight: '300px',
                color: theme.text.primary,
              }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{dailyMd}</ReactMarkdown>
            </article>
          )}
        </div>
      </div>
    </section>
  );
};

export default DailyNotesSection;
