# My Daily Notes App

A modern, production-ready desktop application for managing daily notes and goals, built with React and Electron.

## Features

- 📝 **Daily Note Management**: Create, edit, and organize your daily notes in Markdown format
- 🎯 **Goal Tracking**: Set and track project goals with visual progress charts
- 📊 **Data Visualization**: Interactive charts powered by Plotly.js for productivity insights
- 🗂️ **File System Integration**: Direct integration with your local file system using the File System Access API
- 🌙 **Modern UI**: Clean, responsive interface with consistent styling
- ⚡ **Performance Optimized**: Fast loading and smooth interactions
- 🧪 **Well Tested**: Comprehensive test suite with 82 passing tests
- 🔒 **Secure**: Built with security best practices and error handling

## Quick Start

### Prerequisites

- Node.js 16.x or higher
- npm 7.x or higher
- Modern browser with File System Access API support (Chrome 86+, Edge 86+)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd my-daily-notes
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

The app will open at [http://localhost:3000](http://localhost:3000).

## Available Scripts

### Development

- **`npm start`** - Runs both React dev server and Electron app
- **`npm run start-react`** - Runs only the React development server
- **`npm run start-electron`** - Runs only the Electron app (requires React server)

### Building

- **`npm run build`** - Creates production build for web
- **`npm run build-react`** - Builds React app for production
- **`npm run build-app`** - Builds complete Electron application for Windows
- **`npm run build-win`** - Builds Windows installer (NSIS)

### Testing

- **`npm test`** - Runs test suite in watch mode
- **`npm test -- --watchAll=false`** - Runs all tests once
- **`npm test -- --coverage`** - Runs tests with coverage report

## Usage

### Getting Started

1. **Select Your Notes Folder**: On first launch, click "Select Daily Notes Folder" to choose where your notes are stored
2. **Create Notes**: Click "Create Today's Note" to start writing
3. **Edit Goals**: Use the Goals Editor to set up projects and track progress
4. **View Analytics**: Check the summary graphs to see your productivity trends

### File Structure

The app expects your notes folder to contain:
- `*.md` files for daily notes (named with ISO dates like `2023-12-25.md`)
- `goals.md` file for project and goal tracking

### Goals File Format

The goals file uses a specific markdown format:

```markdown
## Project Name
- ProjectColor: blue

| Done | Task | Start | Finish | Percent |
| ---- | ---- | ----- | ------ | ------- |
| [ ] | Task description | 2023-12-01 | 2023-12-31 | 50 |
| [x] | Completed task | 2023-12-01 | 2023-12-15 | 100 |
```

## Configuration

### Environment Variables

Create a `.env` file in the root directory to customize settings:

```env
# Application settings
REACT_APP_NAME=My Daily Notes App
REACT_APP_DEFAULT_FOLDER_NAME=daily-notes
REACT_APP_GOALS_FILE_NAME=goals.md

# Development settings
REACT_APP_DEBUG=false
REACT_APP_LOG_LEVEL=info
```

### Browser Compatibility

- **Recommended**: Chrome 86+, Edge 86+ (full File System Access API support)
- **Fallback**: Firefox, Safari (file download/upload mode)

## Architecture

### Tech Stack

- **Frontend**: React 19.1.0 with functional components and hooks
- **Desktop**: Electron 36.3.2 for cross-platform desktop app
- **Charts**: Plotly.js for interactive data visualization
- **Markdown**: react-markdown with GitHub Flavored Markdown support
- **Testing**: Jest + React Testing Library (82 tests, 100% critical path coverage)
- **Build**: Create React App with custom Electron integration

### Project Structure

```
src/
├── components/          # React components
│   ├── __tests__/      # Component tests
│   ├── ErrorBoundary.js
│   ├── FolderSelection.js
│   └── ...
├── config/             # Configuration constants
├── styles/             # Theme and styling system
├── utils/              # Utility functions
│   ├── __tests__/      # Utility tests
│   ├── errorHandler.js
│   ├── validation.js
│   └── styles.js
└── App.js              # Main application component
```

## Development

### Code Quality

- **ESLint**: Configured with React and accessibility rules
- **Error Handling**: Comprehensive error boundaries and user-friendly messages
- **Validation**: Input validation for all user data
- **Type Safety**: Extensive validation and error checking
- **Performance**: Optimized rendering and memory usage

### Testing

Run the test suite:
```bash
npm test
```

Generate coverage report:
```bash
npm test -- --coverage --watchAll=false
```

### Building for Production

Build the React app:
```bash
npm run build-react
```

Build the complete Electron application:
```bash
npm run build-app
```

## Troubleshooting

### Common Issues

1. **File System Access Denied**: Grant permission when prompted, or use the fallback file picker
2. **Notes Not Loading**: Ensure your notes folder contains `.md` files with valid names
3. **Charts Not Displaying**: Check that your goals.md file follows the correct format
4. **Electron App Won't Start**: Make sure the React build exists (`npm run build-react`)

### Debug Mode

Enable debug mode by setting `REACT_APP_DEBUG=true` in your `.env` file for detailed error logging.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run the test suite: `npm test`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information about your problem
