  // Example content for demonstrating the application features
  export const getExampleGoalsContent = () => {
    const today = new Date();
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    const nextMonth = new Date(today);
    nextMonth.setMonth(today.getMonth() + 1);
    const nextYear = new Date(today);
    nextYear.setFullYear(today.getFullYear() + 1);

    const formatDate = (date) => date.toISOString().slice(0, 10);

    return `# GOALS

Welcome to your Daily Notes App! This is an example goals file to demonstrate the application's features.

## Current Projects

### Learning & Development
- **React Development**: Building modern web applications
- **Data Visualization**: Creating interactive charts and graphs
- **Personal Productivity**: Optimizing daily workflows

### Health & Wellness
- **Exercise Routine**: Maintaining regular physical activity
- **Mindfulness Practice**: Daily meditation and reflection
- **Nutrition Goals**: Healthy eating habits

# DATA

## Project: React Development
- ProjectColor: blue
| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Complete React Tutorial             | ${formatDate(today)} | ${formatDate(nextWeek)} | 100     |
| [ ]  | Build Portfolio Website             | ${formatDate(nextWeek)} | ${formatDate(nextMonth)} | 0       |

## Project: Health & Wellness
- ProjectColor: green
| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Daily 30min Walk                    | ${formatDate(today)} | ${formatDate(nextMonth)} | 100     |
| [ ]  | Weekly Meal Prep                    | ${formatDate(today)} | ${formatDate(nextYear)} | 25      |

## Project: Personal Productivity
- ProjectColor: purple
| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Setup Daily Notes System            | ${formatDate(today)} | ${formatDate(today)} | 100     |
| [ ]  | Organize Digital Files              | ${formatDate(nextWeek)} | ${formatDate(nextMonth)} | 0       |

# NOTES

This goals file demonstrates:
1. **Project Planning**: Organize your goals into projects and tasks
2. **Timeline Visualization**: See your progress on interactive charts
3. **Status Tracking**: Monitor what's complete, in progress, or planned
4. **Data-Driven Insights**: Use the DATA section for timeline charts

## How to Use This App

1. **Select a Folder**: Choose a folder containing your markdown files
2. **Edit Goals**: Click "Edit Goals" to modify your project timeline
3. **Daily Notes**: Create and edit daily notes with markdown support
4. **Visual Timeline**: View your progress in the interactive charts

## Tips for Success

- Use consistent date formats (YYYY-MM-DD) in the DATA section
- Keep your daily notes organized by date
- Review and update your goals regularly
- Use the preview feature when editing notes

Happy note-taking! 📝✨`;
};

export const getExampleDailyNotes = () => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const twoDaysAgo = new Date(today);
  twoDaysAgo.setDate(today.getDate() - 2);

  const formatDate = (date) => date.toISOString().slice(0, 10);

  return [
    {
      name: formatDate(today),
      content: `# Daily Note - ${formatDate(today)}

## Today's Goals
- [x] Set up Daily Notes App
- [ ] Configure project timeline
- [ ] Write first daily note

## Accomplishments
- Successfully installed and launched the application
- Familiarized with the interface and features
- Created this first daily note

## Learning Notes
- **App Features**: The application supports markdown formatting
- **Goal Tracking**: Projects and tasks can be tracked visually
- **Daily Notes**: Easy to create and organize daily reflections

## Reflections
Today was a productive start to setting up my new productivity system. The app seems intuitive and well-designed. I'm looking forward to using it consistently to track my progress on various projects.

## Ideas for Tomorrow
- Explore the goal editing features in more depth
- Set up a proper project timeline for my current initiatives
- Begin implementing a daily routine with the app

## Gratitude
- Grateful for discovering this helpful productivity tool
- Appreciate the clean and focused design
- Thankful for the ability to organize my thoughts digitally`
    },
    {
      name: formatDate(yesterday),
      content: `# Daily Note - ${formatDate(yesterday)}

## Today's Goals
- [x] Morning workout routine
- [x] Review project requirements
- [ ] Draft project proposal

## Accomplishments
- Completed 45-minute morning workout
- Reviewed all project documentation
- Made significant progress on the draft proposal

## Learning Notes
- **Productivity Technique**: Time-blocking helps maintain focus
- **Health**: Consistent morning exercise boosts energy levels
- **Planning**: Breaking large tasks into smaller steps makes them manageable

## Reflections
Yesterday was a balanced day with good progress on both personal health and professional work. The morning routine set a positive tone for the day. I need to finish the proposal today.

## Next Steps
- Complete and submit the project proposal
- Schedule follow-up meetings with team members
- Plan this week's objectives in the goals section

## Gratitude
- Grateful for good health and energy
- Appreciate supportive colleagues
- Thankful for clear project direction`
    },
    {
      name: formatDate(twoDaysAgo),
      content: `# Daily Note - ${formatDate(twoDaysAgo)}

## Today's Goals
- [x] Morning workout routine
- [x] Read productivity articles
- [ ] Organize workspace

## Accomplishments
- Completed 30-minute morning walk
- Read 3 articles on personal productivity
- Cleaned and organized desk area

## Learning Notes
- **Productivity Insight**: Small daily habits compound over time
- **Health**: Morning exercise improves focus throughout the day
- **Organization**: Clean workspace leads to clearer thinking

## Reflections
Today felt productive and focused. The morning routine is starting to become a habit, which is encouraging. Need to work on consistency with the workspace organization.

## Ideas for Tomorrow
- Try the new note-taking system I researched
- Set up a simple project tracking method
- Continue with the morning routine

## Gratitude
- Grateful for good health and energy
- Appreciate having time for learning and growth
- Thankful for a comfortable workspace`
    }
  ];
};

// Database simulation for storing example content
const DATABASE_KEY = 'daily_notes_example_db';

export const initializeDatabase = () => {
  // Check if database already exists
  if (localStorage.getItem(DATABASE_KEY)) {
    return JSON.parse(localStorage.getItem(DATABASE_KEY));
  }

  // Initialize with example content
  const exampleData = {
    goals: getExampleGoalsContent(),
    notes: getExampleDailyNotes(),
    initialized: true,
    timestamp: new Date().toISOString()
  };

  localStorage.setItem(DATABASE_KEY, JSON.stringify(exampleData));
  return exampleData;
};

export const getDatabaseContent = () => {
  const db = localStorage.getItem(DATABASE_KEY);
  return db ? JSON.parse(db) : null;
};

export const updateDatabaseGoals = (content) => {
  const db = getDatabaseContent() || {};
  db.goals = content;
  db.timestamp = new Date().toISOString();
  localStorage.setItem(DATABASE_KEY, JSON.stringify(db));
};

export const updateDatabaseNote = (noteName, content) => {
  const db = getDatabaseContent() || { notes: [] };
  const noteIndex = db.notes.findIndex(note => note.name === noteName);

  if (noteIndex >= 0) {
    db.notes[noteIndex].content = content;
  } else {
    db.notes.push({ name: noteName, content });
  }

  db.timestamp = new Date().toISOString();
  localStorage.setItem(DATABASE_KEY, JSON.stringify(db));
};

export const hasExistingContent = (goalsMd, dailyNoteNames) => {
  // Check if there's meaningful content beyond default messages
  const hasGoals = goalsMd &&
    goalsMd.trim() !== '' &&
    !goalsMd.includes('Please select a folder') &&
    !goalsMd.includes('No Markdown files found');

  const hasNotes = dailyNoteNames && dailyNoteNames.length > 0;

  return hasGoals || hasNotes;
};

export const shouldLoadExampleContent = (goalsMd, dailyNoteNames, isFolderSelected) => {
  // Load example content if no folder is selected and no meaningful content exists
  return !isFolderSelected && !hasExistingContent(goalsMd, dailyNoteNames);
};
