// App.js
import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import HeaderTimeline from './components/HeaderTimeline';
import SummaryGraph from './components/SummaryGraph';
import FolderSelection from './components/FolderSelection';
import GoalsSection from './components/GoalsSection';
import DailyNotesSection from './components/DailyNotesSection';
import GoalsEditor from './components/GoalsEditor';
import ThemeToggle from './components/ThemeToggle';
import LoadingSpinner from './components/LoadingSpinner';
import { ToastContainer } from './components/Toast';
import { useTheme } from './contexts/ThemeContext';
import { useToast } from './hooks/useToast';
import { APP_CONFIG, FILE_CONFIG, FEATURES } from './config/constants';
import { logError, handleFileSystemError, showErrorToUser } from './utils/errorHandler';
import { userAction, fileOperation } from './utils/logger';
import { sanitizeMarkdown, sanitizeFileName, validateFileSize } from './utils/security';
import { startTimer } from './utils/performance';
import { spacing } from './styles/theme';
import { getExampleGoalsContent, getExampleDailyNotes, shouldLoadExampleContent, initializeDatabase, getDatabaseContent, updateDatabaseGoals, updateDatabaseNote } from './utils/exampleContent';

function App() {
  // ── Theme ───────────────────────────────────────────────────────────────
  const { theme } = useTheme();

  // ── Toast notifications ─────────────────────────────────────────────────
  const { toasts, removeToast, showSuccess, showError, showInfo } = useToast();

  // ── State ───────────────────────────────────────────────────────────────
  const [goalsMd, setGoalsMd] = useState('');
  const [dailyMd, setDailyMd] = useState('Please select a folder with your daily notes.');
  const [dailyNoteNames, setDailyNoteNames] = useState([]);
  const [selectedNote, setSelectedNote] = useState(null);
  const [isEditingNote, setIsEditingNote] = useState(false);
  const [isEditingGoals, setIsEditingGoals] = useState(false);
  const [isFolderSelected, setIsFolderSelected] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const todayISO = useMemo(() => new Date().toISOString().slice(0, 10), []);

  // Folder/File handling
  const [folderHandle, setFolderHandle] = useState(null);
  const fileHandlesRef = useRef({});
  const fileInputRef = useRef(null);
  const goalsFileHandleRef = useRef(null);

  // Database initialization effect
  useEffect(() => {
    // Initialize database with example content on first load
    initializeDatabase();
  }, []);

  // ── Folder Selection & File Loading ───────────────────────────────────────
  const handleSelectFolder = useCallback(async () => {
    const timer = startTimer('folder-selection');
    try {
      setError(null);
      userAction('select_folder_clicked');

      if (FEATURES.fileSystemAccess) {
        const handle = await window.showDirectoryPicker();
        await setupSelectedFolder(handle);
      } else {
        fileInputRef.current.click();
      }

      timer.end({ success: true });
    } catch (err) {
      timer.end({ success: false, error: err.message });
      const appError = handleFileSystemError(err, 'select folder');
      logError(appError, 'handleSelectFolder');
      setError(appError.message);
      showErrorToUser(appError);
    }
  }, []);



  const setupSelectedFolder = async (handle) => {
    try {
      setFolderHandle(handle);
      setIsFolderSelected(true);

      // Load goals file
      try {
        const goalsFileHandle = await handle.getFileHandle(FILE_CONFIG.goalsFileName, { create: true });
        goalsFileHandleRef.current = goalsFileHandle;

        const goalsFile = await goalsFileHandle.getFile();
        const goalsContent = await goalsFile.text();
        setGoalsMd(goalsContent);
      } catch (goalsError) {
        const appError = handleFileSystemError(goalsError, 'load goals file');
        logError(appError, 'setupSelectedFolder - goals');
        setGoalsMd(''); // Continue with empty goals if file doesn't exist
      }

      await loadDailyNoteFiles(handle);
    } catch (err) {
      const appError = handleFileSystemError(err, 'setup folder');
      logError(appError, 'setupSelectedFolder');
      setError(appError.message);
      showErrorToUser(appError);
    }
  };

  const loadDailyNoteFiles = async (handle) => {
    try {
      const names = [];
      const handles = {};

      for await (const entry of handle.values()) {
        if (entry.kind === 'file' &&
            FILE_CONFIG.supportedExtensions.some(ext => entry.name.endsWith(ext)) &&
            entry.name !== FILE_CONFIG.goalsFileName) {
          const noteName = entry.name.replace(/\.(md|markdown)$/i, '');
          names.push(noteName);
          handles[noteName] = entry;
        }
      }

      setDailyNoteNames(names);
      fileHandlesRef.current = handles;

      if (names.length) {
        const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
        setSelectedNote(sorted[sorted.length - 1]);
      } else {
        setDailyMd('No Markdown files found in this folder.');
      }
    } catch (err) {
      const appError = handleFileSystemError(err, 'load daily note files');
      logError(appError, 'loadDailyNoteFiles');
      setError(appError.message);
      showErrorToUser(appError);
    }
  };

  const handleFallbackFiles = useCallback((e) => {
    try {
      setError(null);
      const files = Array.from(e.target.files);
      const names = [];
      const handles = {};

      files.forEach((file) => {
        if (FILE_CONFIG.supportedExtensions.some(ext => file.name.endsWith(ext)) &&
            file.name !== FILE_CONFIG.goalsFileName) {
          const noteName = file.name.replace(/\.(md|markdown)$/i, '');
          names.push(noteName);
          handles[noteName] = file;
        }
      });

      setDailyNoteNames(names);
      fileHandlesRef.current = handles;
      setIsFolderSelected(true);

      if (names.length) {
        const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
        setSelectedNote(sorted[sorted.length - 1]);
      } else {
        setDailyMd('No Markdown files found in this folder.');
      }
    } catch (err) {
      const appError = handleFileSystemError(err, 'process selected files');
      logError(appError, 'handleFallbackFiles');
      setError(appError.message);
      showErrorToUser(appError);
    }
  }, []);

  // ── Goals Saving Method ─────────────────────────────────────────────────
  const saveGoalsFile = async (content) => {
    const timer = startTimer('save-goals-file');
    try {
      setError(null);

      // Validate and sanitize content
      if (!validateFileSize(content)) {
        throw new Error('Goals file is too large');
      }

      const sanitizedContent = sanitizeMarkdown(content);

      // Update database with new content
      updateDatabaseGoals(sanitizedContent);

      // Check if we have a file handle (folder selected) or need to use fallback
      if (goalsFileHandleRef.current && 'createWritable' in goalsFileHandleRef.current) {
        // File System Access API - write to selected folder
        const writable = await goalsFileHandleRef.current.createWritable();
        await writable.write(sanitizedContent);
        await writable.close();

        setGoalsMd(sanitizedContent);
        setIsEditingGoals(false);

        fileOperation('write', FILE_CONFIG.goalsFileName, { size: sanitizedContent.length });
        showSuccess('Goals saved successfully!');
      } else {
        // Fallback: download the file
        const blob = new Blob([sanitizedContent], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = FILE_CONFIG.goalsFileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        setGoalsMd(sanitizedContent);
        setIsEditingGoals(false);

        fileOperation('download', FILE_CONFIG.goalsFileName, { size: sanitizedContent.length });
        showInfo('Goals file downloaded. Please save it to your notes folder.');
      }

      userAction('goals_saved');
      timer.end({ success: true, size: sanitizedContent.length });
    } catch (err) {
      timer.end({ success: false, error: err.message });
      const appError = handleFileSystemError(err, 'save goals file');
      logError(appError, 'saveGoalsFile');
      setError(appError.message);
      showError('Failed to save goals file');
    }
  };

  // ── Note Content Loading ────────────────────────────────────────────────
  const loadNoteContent = async (noteName) => {
    try {
      setError(null);
      const handleOrFile = fileHandlesRef.current[noteName];

      if (!handleOrFile) {
        setDailyMd('Note not found.');
        return;
      }

      // Check if this is a database note
      if (handleOrFile.isDatabaseNote) {
        setDailyMd(handleOrFile.content);
        return;
      }

      let file;
      if ('getFile' in handleOrFile) {
        file = await handleOrFile.getFile();
      } else {
        file = handleOrFile;
      }

      const text = await file.text();
      setDailyMd(text);
    } catch (err) {
      const appError = handleFileSystemError(err, 'load note content');
      logError(appError, 'loadNoteContent');
      setError(appError.message);
      setDailyMd('Error reading note.');
    }
  };

  useEffect(() => {
    if (selectedNote) loadNoteContent(selectedNote);
  }, [selectedNote]);

  // ── Note Handlers ─────────────────────────────────────────────────────────
  const handleNoteSelect = useCallback((noteName) => {
    setSelectedNote(noteName);
    setIsEditingNote(false);
    loadNoteContent(noteName);
  }, []);

  const reloadGoals = async () => {
    try {
      if (goalsFileHandleRef.current) {
        const file = await goalsFileHandleRef.current.getFile();
        const content = await file.text();
        setGoalsMd(content);
        showSuccess('Goals reloaded successfully!');
      } else {
        showInfo('No goals file handle available to reload.');
      }
    } catch (error) {
      console.error('Error reloading goals:', error);
      showError('Failed to reload goals.');
    }
  };

  const handleCreateNote = useCallback(() => {
    setSelectedNote(todayISO);
    setDailyMd('');
    setIsEditingNote(true);
    if (!dailyNoteNames.includes(todayISO)) {
      setDailyNoteNames((prev) => [...prev, todayISO]);
    }
  }, [todayISO, dailyNoteNames]);


  const handleSaveNote = useCallback(async (content) => {
    const timer = startTimer('save-note');
    try {
      setError(null);

      // Validate and sanitize content
      if (!validateFileSize(content)) {
        throw new Error('Note content is too large');
      }

      const sanitizedContent = sanitizeMarkdown(content);
      const sanitizedFileName = sanitizeFileName(selectedNote);

      // Update database with new content
      updateDatabaseNote(selectedNote, sanitizedContent);

      const handleOrFile = fileHandlesRef.current[selectedNote];

      if (handleOrFile && 'createWritable' in handleOrFile) {
        // File System Access API
        const writable = await handleOrFile.createWritable();
        await writable.write(sanitizedContent);
        await writable.close();
        setDailyMd(sanitizedContent);
        setIsEditingNote(false);

        fileOperation('write', `${sanitizedFileName}.md`, { size: sanitizedContent.length });
        showSuccess('Note saved successfully!');
      } else if (handleOrFile && handleOrFile.isDatabaseNote) {
        // Database note - update in memory
        setDailyMd(sanitizedContent);
        setIsEditingNote(false);

        // Update the mock handle
        fileHandlesRef.current[selectedNote] = {
          ...handleOrFile,
          content: sanitizedContent
        };

        showSuccess('Note saved to database!');
      } else {
        // Fallback: download file
        const newFile = new Blob([sanitizedContent], { type: 'text/markdown' });
        const fileUrl = URL.createObjectURL(newFile);
        const a = document.createElement('a');
        a.href = fileUrl;
        a.download = `${sanitizedFileName}.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(fileUrl);
        setDailyMd(sanitizedContent);
        setIsEditingNote(false);

        fileOperation('download', `${sanitizedFileName}.md`, { size: sanitizedContent.length });
        showInfo('Note downloaded to your device!');
      }

      userAction('note_saved', { fileName: sanitizedFileName });
      timer.end({ success: true, size: sanitizedContent.length });
    } catch (err) {
      timer.end({ success: false, error: err.message });
      const appError = handleFileSystemError(err, 'save note');
      logError(appError, 'handleSaveNote');
      setError(appError.message);
      showErrorToUser(appError);
    }
  }, [selectedNote]);

  const handleLoadGoals = async () => {
    try {
      if (window.showOpenFilePicker) {
        const [fileHandle] = await window.showOpenFilePicker({
          types: [
            {
              description: 'Markdown Files',
              accept: {
                'text/markdown': ['.md', '.markdown'],
              },
            },
          ],
          multiple: false,
        });
        goalsFileHandleRef.current = fileHandle;
        const file = await fileHandle.getFile();
        const content = await file.text();
        setGoalsMd(content);
        showSuccess('Goals file loaded successfully!');
      }
    } catch (err) {
      const appError = handleFileSystemError(err, 'load goals file');
      logError(appError, 'handleLoadGoals');
      showError('Failed to load goals file.');
    }
  };

  const handleEditNote = useCallback(() => setIsEditingNote(true), []);

  // ── Example Content Loading ──────────────────────────────────────────────
  const loadExampleContent = useCallback(() => {
    try {
      // Try to load from database first
      const dbContent = getDatabaseContent();

      if (dbContent) {
        // Load goals from database
        setGoalsMd(dbContent.goals);

        // Load daily notes from database
        const noteNames = dbContent.notes.map(note => note.name);
        setDailyNoteNames(noteNames);

        // Create mock file handles for database notes
        const mockHandles = {};
        dbContent.notes.forEach(note => {
          mockHandles[note.name] = {
            isDatabaseNote: true,
            content: note.content,
            getFile: () => Promise.resolve({
              text: () => Promise.resolve(note.content)
            })
          };
        });
        fileHandlesRef.current = mockHandles;

        // Select the most recent note
        if (noteNames.length > 0) {
          const sorted = [...noteNames].sort((a, b) => new Date(b) - new Date(a));
          setSelectedNote(sorted[0]);
        }

        userAction('database_content_loaded');
        showInfo('Loaded saved content from database.');
      } else {
        // Fallback to example content
        const exampleGoals = getExampleGoalsContent();
        setGoalsMd(exampleGoals);

        const exampleNotes = getExampleDailyNotes();
        const noteNames = exampleNotes.map(note => note.name);
        setDailyNoteNames(noteNames);

        // Create mock file handles for example notes
        const mockHandles = {};
        exampleNotes.forEach(note => {
          mockHandles[note.name] = {
            isExample: true,
            content: note.content,
            getFile: () => Promise.resolve({
              text: () => Promise.resolve(note.content)
            })
          };
        });
        fileHandlesRef.current = mockHandles;

        // Select the most recent note
        if (noteNames.length > 0) {
          const sorted = [...noteNames].sort((a, b) => new Date(b) - new Date(a));
          setSelectedNote(sorted[0]);
        }

        userAction('example_content_loaded');
        showInfo('Welcome! Example content loaded to demonstrate app features.');
      }
    } catch (err) {
      const appError = handleFileSystemError(err, 'load example content');
      logError(appError, 'loadExampleContent');
      setError(appError.message);
      showError('Failed to load example content');
    }
  }, []);

  // ── Goals Loading ─────────────────────────────────────────────────────────
  useEffect(() => {
    // Only load public goals.md if no folder is selected
    if (!isFolderSelected) {
      fetch('/goals.md')
        .then((r) => {
          if (!r.ok) throw new Error('Failed to load goals.md');
          return r.text();
        })
        .then((content) => {
          setGoalsMd(content);
          showInfo('Loaded example goals from public/goals.md');
        })
        .catch((err) => {
          console.error('Failed to load public goals.md:', err);
          // Fallback to database/example content if public file fails
          if (shouldLoadExampleContent(goalsMd, dailyNoteNames, false)) {
            loadExampleContent();
          }
        });
    }
  }, [isFolderSelected, loadExampleContent]);

  // ── Example Content Auto-loading ─────────────────────────────────────────
  useEffect(() => {
    // Only load example content if no folder is selected AND no goals are loaded
    if (!isFolderSelected && shouldLoadExampleContent(goalsMd, dailyNoteNames, false)) {
      loadExampleContent();
    }
  }, [goalsMd, dailyNoteNames, isFolderSelected, loadExampleContent]);

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: 'system-ui, -apple-system, sans-serif',
    backgroundColor: theme.background.default,
    color: theme.text.primary,
    minHeight: '100vh',
    padding: `${spacing.md}`,
    '@media (max-width: 768px)': {
      padding: spacing.sm,
    },
  };

  const headerStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xl,
    padding: `${spacing.lg} 0`,
    borderBottom: `1px solid ${theme.border.light}`,
  };

  const errorStyle = {
    backgroundColor: theme.error.background,
    color: theme.error.dark,
    padding: spacing.md,
    borderRadius: '4px',
    marginBottom: spacing.md,
    border: `1px solid ${theme.error.light}`,
  };

  return (
    <div style={containerStyle}>
      <header style={headerStyle}>
        <h1 style={{ margin: 0, color: theme.text.primary }}>
          {APP_CONFIG.name}
        </h1>
        <ThemeToggle />
      </header>

      {/* Error Display */}
      {error && (
        <div style={errorStyle}>
          <strong>Error:</strong> {error}
          <button
            onClick={() => setError(null)}
            style={{
              float: 'right',
              background: 'none',
              border: 'none',
              color: theme.error.dark,
              cursor: 'pointer',
              fontSize: '1.2rem',
            }}
          >
            ×
          </button>
        </div>
      )}

      <HeaderTimeline />
      <main>
        {/* 1) Always show Folder Selection if no folder selected */}
        {!isFolderSelected && (
          <FolderSelection
            supportsDirectoryPicker={FEATURES.fileSystemAccess}
            DEFAULT_FOLDER_NAME={FILE_CONFIG.defaultFolderName}
            handleSelectFolder={handleSelectFolder}
            fileInputRef={fileInputRef}
            handleFallbackFiles={handleFallbackFiles}
          />
        )}

        {/* Rest of the app is conditionally rendered only after folder selection */}
        {isFolderSelected && (
          <>
            {/* Goals Section */}
            {isEditingGoals ? (
              <GoalsEditor
                initialContent={goalsMd}
                onSave={saveGoalsFile}
                onCancel={() => setIsEditingGoals(false)}
              />
            ) : (
              <>
                <div style={{ flex: '1 1 50%', minWidth: '300px', display: 'flex', flexDirection: 'column', height: '100%' }}>
                  <GoalsSection
                    goalsMd={goalsMd}
                    isEditingGoals={isEditingGoals}
                    onEditGoals={() => setIsEditingGoals(true)}
                    onSaveGoals={(content) => saveGoalsFile(content)}
                    onLoadGoals={handleLoadGoals}
                  />
                </div>
              </>
            )}

            {/* Weekly summary graph */}
            {dailyNoteNames.length > 0 && (
              <SummaryGraph 
                dailyNoteNames={dailyNoteNames} 
                fileHandlesRef={fileHandlesRef} 
              />
            )}

            {/* Daily Notes Section */}
            {dailyNoteNames.length > 0 && (
              <DailyNotesSection
                dailyMd={dailyMd}
                dailyNoteNames={dailyNoteNames}
                selectedNote={selectedNote}
                isEditingNote={isEditingNote}
                onSelectNote={handleNoteSelect}
                onCreateNote={handleCreateNote}
                onEditNote={handleEditNote}
                onSaveNote={handleSaveNote}
              />
            )}
          </>
        )}

        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          webkitdirectory="true"
          directory="true"
          multiple
          onChange={handleFallbackFiles}
        />
      </main>

      {/* Toast notifications */}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </div>
  );
}

export default App;
