// components/GoalsSection.js
import React, { useEffect, useRef } from 'react';
import Plotly from 'plotly.js-dist-min';
import GoalsEditor from './GoalsEditor';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius, shadows } from '../styles/theme';

const GoalsSection = ({ goalsMd, isEditingGoals, onEditGoals, onSaveGoals, onLoadGoals }) => {
  const { theme, components } = useTheme();
  const fullPlotRef = useRef(null);
  const weekPlotRef = useRef(null);
  const fullContainerRef = useRef(null);
  const weekContainerRef = useRef(null);
  const todayISO = new Date().toISOString().slice(0, 10);

  // Effect to generate Plotly charts from the goals markdown
  useEffect(() => {
    if (!goalsMd || isEditingGoals) return;

    try {
      // --- Extract the DATA section from goalsMd ---
      const dataMatch = /# DATA([\s\S]*?)(?:# NOTES|$)/.exec(goalsMd);
      if (!dataMatch) return;
      const dataSection = dataMatch[1].trim();

      // Early exit if no data section content
      if (!dataSection.trim()) {
        // Clear existing plots if they exist
        if (fullPlotRef.current) {
          Plotly.purge(fullPlotRef.current);
        }
        if (weekPlotRef.current) {
          Plotly.purge(weekPlotRef.current);
        }
        return;
      }

      // Parse table helper function (used to extract tasks)
      function parseTable(table) {
        const rows = table
          .split('\n')
          .filter(Boolean)
          .map((r) => r.split('|').map((c) => c.trim()));
        const headers = rows[0].filter(Boolean);
        const items = rows.slice(2).map((row) => {
          const obj = {};
          headers.forEach((h, i) => (obj[h] = row[i + 1] || ''));
          if (obj.Task.startsWith('-')) {
            obj.isSubtask = true;
            obj.Task = obj.Task.slice(1).trim();
          } else {
            obj.isSubtask = false;
          }
          if (obj.Done === '[x]') {
            obj.State = 'Complete';
          } else if (obj.Done === '[ ]') {
            obj.State = 'Incomplete';
          }
          return obj;
        });

        const out = [];
        let current = null;
        for (let i = 0; i < items.length; i++) {
          if (!items[i].isSubtask) {
            current = items[i];
            // Add visual indicator for main tasks that have subtasks
            current.hasSubtasks = false;
            out.push(current);
            const subtasks = [];
            let j = i + 1;
            while (j < items.length && items[j].isSubtask) {
              items[j].parentTask = current.Task;
              items[j].isSubtask = true; // Ensure this property is set
              subtasks.push(items[j]);
              j++;
            }
            if (subtasks.length) {
              current.hasSubtasks = true; // Mark that this task has subtasks
              const startDate = new Date(current.Start);
              const finishDate = new Date(current.Finish);
              const msPerDay = 86400000;
              const totalDays = Math.ceil((finishDate - startDate) / msPerDay);
              const totalSubtasks = subtasks.length;
              let taskIndex = 0;
              for (let i = 0; i < totalSubtasks; i++) {
                const dayIndex = i / (totalSubtasks / totalDays);
                const dayStart = new Date(startDate.getTime() + dayIndex * msPerDay);
                let dayFinish;
                if (dayIndex < totalDays - 1) {
                  dayFinish = new Date(dayStart.getTime() + msPerDay + 1);
                } else {
                  dayFinish = new Date(finishDate.getTime());
                }
                const st = subtasks[taskIndex];
                st.Start = dayStart.toISOString().slice(0, 10);
                st.Finish = dayFinish.toISOString().slice(0, 10);
                st.Percent = st.Percent || '0';
                st.State = st.State || 'Incomplete';
                taskIndex++;
                if (taskIndex >= totalSubtasks) {
                  break;
                }
              }
            }
            out.push(...subtasks);
            i = j - 1;
          }
        }
        return out;
      }

      // Parse projects from the DATA section
      function parseProjects(text) {
        const lines = text.split('\n');
        const projects = [];
        let project = null;
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.startsWith('## ')) {
            project = { ProjectName: line.slice(3), Tasks: [] };
          } else if (line.startsWith('- ProjectColor:') && project) {
            project.ProjectColor = line.split(':')[1].trim();
          } else if (line.startsWith('|') && project) {
            const block = [];
            while (i < lines.length && lines[i].startsWith('|')) {
              block.push(lines[i]);
              i++;
            }
            project.Tasks = parseTable(block.join('\n'));
            projects.push(project);
            project = null;
            i--;
          }
        }
        return projects;
      }

      // Generate time marks for Plotly
      function generateTimeMarks(year) {
        const months = [
          'JANUARY',
          'FEBRUARY',
          'MARCH',
          'APRIL',
          'MAY',
          'JUNE',
          'JULY',
          'AUGUST',
          'SEPTEMBER',
          'OCTOBER',
          'NOVEMBER',
          'DECEMBER',
        ];
        const curMonth = new Date().getMonth();
        const nextMonth = (curMonth + 1) % 12;
        const marks = [curMonth, nextMonth].map((m) => {
          const start = `${year}-${String(m + 1).padStart(2, '0')}-01`;
          const dayCount = new Date(year, m + 1, 0).getDate();
          const finish = `${year}-${String(m + 1).padStart(2, '0')}-${dayCount}`;
          return {
            Task: months[m],
            Start: start,
            Finish: finish,
            Percent: '100',
            State: 'Complete',
          };
        });
        return [{ ProjectName: 'TIMEMARKS', ProjectColor: 'blue', Tasks: marks }];
      }

      const userProjects = parseProjects(dataSection);
      const allStarts = userProjects.flatMap(p => p.Tasks.map(t => t.Start)).filter(Boolean);
      const years = allStarts.map(s => +s.slice(0, 4)).sort();
      const refYear = years[0] || new Date().getFullYear();
      const twoMonthsAgo = new Date();
      twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 1);

      const entries = [
        ...generateTimeMarks(refYear),
        ...userProjects,
      ]
        .map(proj => ({
          ...proj,
          Tasks: proj.Tasks.filter(t => {
            const finishDate = new Date(t.Finish);
            return !isNaN(finishDate.getTime()) && finishDate > twoMonthsAgo;
          }),
        }))
        .filter(p => p.Tasks.length);

      // Plotly data arrays and layout settings
      const data = [];
      const shapes = [];
      const fullA = [];
      const weekA = [];
      const yTicks = [];
      let counter = 0;
      const weekStart = new Date();
      weekStart.setHours(0, 0, 0, 0);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 7);
      const wsISO = weekStart.toISOString().slice(0, 10);
      const weISO = weekEnd.toISOString().slice(0, 10);

      function dateAtPercent(s, f, p) {
        const sd = new Date(s),
          fd = new Date(f);
        if (isNaN(sd.getTime()) || isNaN(fd.getTime())) return null;
        const endDate = new Date(sd.getTime() + (fd - sd) * (p / 100));
        return endDate.toISOString().slice(0, 10);
      }

      function addRect(x0, x1, c, y0, y1, o = 0.8) {
        if (!x0 || !x1 || isNaN(new Date(x0).getTime()) || isNaN(new Date(x1).getTime())) {
          console.warn('Skipping shape due to invalid dates:', x0, x1);
          return;
        }
        shapes.push({
          type: 'rect',
          xref: 'x',
          yref: 'y',
          x0,
          y0,
          x1,
          y1,
          fillcolor: c,
          opacity: o,
          line: { width: 2, color: theme.border.dark },
          layer: 'below',
        });
      }

      // Helper to lighten color by percent (0-100)
      function lightenColor(hex, percent) {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
          (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
          (B < 255 ? (B < 1 ? 0 : B) : 255))
          .toString(16).slice(1);
      }

      entries.forEach((project) => {
        if (project.ProjectName === 'TIMEMARKS') return;

        let inner = 0;
        project.Tasks.forEach((task) => {
          const taskStart = task.Start;
          const taskFinish = task.Finish;
          const taskPercent = parseFloat(task.Percent) || 0;

          // Determine color based on state and whether it's a subtask
          let color = theme.chart.incomplete;
          if (task.State === 'Complete') {
            color = theme.chart.complete;
          } else if (task.State === 'InProgress') {
            color = theme.chart.inProgress;
          }

          // Adjust color for subtasks to show hierarchy
          if (task.isSubtask) {
            // Lighten the color for subtasks to show they are subordinate
            color = lightenColor(color, 30); // Lighten by 30%
          }

          // Add main task bar
          addRect(taskStart, taskFinish, color, inner - 0.4, inner + 0.4);

          // Add progress indicator if task is partially complete
          if (taskPercent > 0 && taskPercent < 100) {
            const progressDate = dateAtPercent(taskStart, taskFinish, taskPercent);
            if (progressDate) {
              addRect(taskStart, progressDate, theme.chart.progress, inner - 0.4, inner + 0.4, 0.8);
            }
          }

          // Add task to data arrays with improved hover info
          const hoverText = `<b>${task.Task}</b><br>Start: ${taskStart}<br>Finish: ${taskFinish}<br>Status: ${task.State}`;

          // Full timeline scatter plot
          data.push({
            type: 'scatter',
            mode: 'lines',
            x: [taskStart, taskFinish],
            y: [inner, inner],
            hoverinfo: 'text',
            text: hoverText,
            line: { width: 12, color: 'transparent' },
            name: task.Task,
          });

          // Full annotations for task labels with arrow prefix for subtasks
          fullA.push({
            x: new Date(taskFinish).getTime(),
            y: inner,
            xref: 'x',
            yref: 'y',
            text: (task.isSubtask ? '↳ ' : '') + task.Task,
            showarrow: false,
            font: {
              family: 'Arial Black, sans-serif',
              size: task.isSubtask ? 10 : 12,
              color: '#000',
            },
            align: 'left',
          });

          const ts = new Date(taskStart);
          const tf = new Date(taskFinish);
          if (!isNaN(ts.getTime()) && !isNaN(tf.getTime()) && tf >= weekStart && ts <= weekEnd) {
            const vs = ts < weekStart ? weekStart : ts;
            const ve = tf > weekEnd ? weekEnd : tf;
            const midpoint = new Date((vs.getTime() + ve.getTime()) / 2);
            if (!isNaN(midpoint.getTime())) {
              weekA.push({
                x: ve,
                y: inner,
                xref: 'x',
                yref: 'y',
                text: (task.isSubtask ? '↳ ' : '') + task.Task,
                showarrow: false,
                font: {
                  family: 'Arial Black, sans-serif',
                  size: task.isSubtask ? 10 : 12,
                  color: '#000',
                },
                align: 'center',
                borderwidth: 1,
                borderpad: 2,
              });
            } else {
              console.warn(
                `Skipping weekly annotation for task "${task.Task}" due to invalid midpoint calculation for dates: ${vs.toISOString().slice(
                  0,
                  10
                )} to ${ve.toISOString().slice(0, 10)}`
              );
            }
          }

          // Add y-axis label with indentation and arrow prefix for subtasks
          const labelPrefix = task.isSubtask ? '  ↳ ' : '';
          yTicks.push(`${labelPrefix}${task.Task}`);

          inner++;
        });

        counter += inner;
        // Add spacing after project tasks
        for (let i = 0; i < inner; i++) yTicks.push('');
      });

      const finalTickVals = yTicks.map((_, i) => i * 0.2);
      shapes.push({
        type: 'line',
        x0: todayISO,
        x1: todayISO,
        y0: 0,
        y1: counter,
        line: { color: 'darkred', width: 2, dash: 'dot' },
      });

      const fullLayout = {
        autosize: true,
        margin: { l: 150, r: 150, t: 30, b: 50 },
        xaxis: {
          type: 'date',
          rangeselector: {
            buttons: [
              { count: 7, step: 'day', stepmode: 'backward', label: '1w' },
              { count: 1, step: 'month', stepmode: 'backward', label: '1m' },
              { step: 'all', label: 'All' },
            ],
          },
        },
        yaxis: {
          ticktext: yTicks,
          tickvals: finalTickVals,
          autorange: 'reversed',
        },
        shapes,
        annotations: fullA,
        showlegend: false,
      };

      const weekLayout = {
        autosize: true,
        margin: { l: 150, r: 30, t: 30, b: 50 },
        xaxis: { type: 'date', autorange: false, range: [wsISO, weISO] },
        yaxis: {
          ticktext: yTicks,
          tickvals: finalTickVals,
          autorange: 'reversed',
        },
        shapes,
        annotations: weekA,
        showlegend: false,
      };

      Plotly.react(fullPlotRef.current, data, fullLayout, { responsive: true });
      Plotly.react(weekPlotRef.current, data, weekLayout, { responsive: true });
    } catch (error) {
      console.error('Error generating goals timeline charts:', error);
      // Clear plots on error
      if (fullPlotRef.current) {
        Plotly.purge(fullPlotRef.current);
      }
      if (weekPlotRef.current) {
        Plotly.purge(weekPlotRef.current);
      }
    }
  }, [goalsMd, isEditingGoals, todayISO]);

  // Resize observer for responsive charts
  useEffect(() => {
    if (isEditingGoals) return;
    if (!fullContainerRef.current || !weekContainerRef.current) return;

    const roFull = new ResizeObserver(() => {
      Plotly.Plots.resize(fullPlotRef.current);
    });
    const roWeek = new ResizeObserver(() => {
      Plotly.Plots.resize(weekPlotRef.current);
    });

    roFull.observe(fullContainerRef.current);
    roWeek.observe(weekContainerRef.current);

    return () => {
      roFull.disconnect();
      roWeek.disconnect();
    };
  }, [isEditingGoals]);

  const sectionStyle = {
    marginBottom: spacing.xl,
  };

  const headerStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  };

  const titleStyle = {
    margin: 0,
    color: theme.text.primary,
    fontSize: '1.5rem',
    fontWeight: '600',
  };

  const editButtonStyle = {
    ...components.button.base,
    ...components.button.primary,
  };

  return (
    <div style={{ ...components.goalsSection, flex: '1 1 auto', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      <div style={{ padding: spacing.medium, borderBottom: `1px solid ${theme.border.medium}`, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexShrink: 0 }}>
        <h2 style={{ ...theme.typography.h2, margin: 0 }}>Goals</h2>
        <div>
          <button onClick={onLoadGoals} style={{ ...components.button, marginRight: spacing.small }}>Load Goals</button>
          <button onClick={handleReloadGoals} style={{ ...components.button, marginRight: spacing.small }}>Reload Goals</button>
          <button onClick={() => onEditGoals(true)} style={{ ...components.button }}>Edit</button>
        </div>
      </div>
      {isEditingGoals ? (
        <GoalsEditor
          initialContent={goalsMd}
          onSave={onSaveGoals}
          onCancel={() => onSaveGoals(goalsMd)}
        />
      ) : (
        <div style={{ flex: '1 1 auto', overflowY: 'auto', padding: spacing.medium }}>
          <div ref={fullContainerRef} style={{ width: '100%', height: '400px', marginBottom: spacing.medium }}>
            <div ref={fullPlotRef} style={{ width: '100%', height: '100%' }}></div>
          </div>
          <div ref={weekContainerRef} style={{ width: '100%', height: '250px' }}>
            <div ref={weekPlotRef} style={{ width: '100%', height: '100%' }}></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GoalsSection;
