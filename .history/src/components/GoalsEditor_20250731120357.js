// components/GoalsEditor.js
import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { spacing, borderRadius } from '../styles/theme';

const GoalsEditor = ({ initialContent, onSave, onCancel }) => {
  const { theme, components } = useTheme();
  const [projects, setProjects] = useState([]);

  // Parse initial content when component mounts
  useEffect(() => {
    parseGoalsContent(initialContent);
  }, [initialContent]);

  const parseGoalsContent = (content) => {
    const lines = content.split('\n');
    const parsedProjects = [];
    let currentProject = null;
    let inDataSection = false;

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // Check if we're entering the DATA section
      if (trimmedLine === '# DATA') {
        inDataSection = true;
        return;
      }

      // Only process content within the DATA section
      if (!inDataSection) return;

      // Stop processing if we hit another main section
      if (trimmedLine.startsWith('# ') && trimmedLine !== '# DATA') {
        inDataSection = false;
        return;
      }

      if (trimmedLine.startsWith('## ')) {
        // New project - save previous project if exists
        if (currentProject) {
          parsedProjects.push(currentProject);
        }
        currentProject = {
          name: trimmedLine.slice(3).trim(),
          color: '',
          tasks: []
        };
      } else if (trimmedLine.startsWith('- ProjectColor:') && currentProject) {
        currentProject.color = trimmedLine.split(':')[1].trim();
      } else if (trimmedLine.startsWith('| ') && currentProject) {
        // Skip table header and separator rows
        if (trimmedLine.includes('| Done | Task |') ||
            trimmedLine.includes('| ---- |') ||
            trimmedLine === '|') {
          return;
        }

        // Parse task row
        const columns = line.split('|').map(col => col.trim());
        if (columns.length >= 6) {
          const taskText = columns[2] || '';

          // Check if task is a subtask (starts with dash)
          const isSubtask = taskText.startsWith('- ');
          const cleanTaskText = isSubtask ? taskText.substring(2) : taskText;

          const task = {
            done: columns[1] || '[ ]',
            task: cleanTaskText,
            start: columns[3] || '',
            finish: columns[4] || '',
            percent: columns[5] || '0',
            isSubtask: isSubtask
          };

          // Only add non-empty tasks
          if (task.task.trim()) {
            currentProject.tasks.push(task);
          }
        }
      }
    });

    // Add last project
    if (currentProject) {
      parsedProjects.push(currentProject);
    }

    setProjects(parsedProjects);
  };

  const generateMarkdownContent = () => {
    let content = '# DATA\n\n';

    content += projects.map(project => {
      let projectContent = `## ${project.name}\n`;
      projectContent += `- ProjectColor: ${project.color}\n\n`;
      projectContent += `| Done | Task                                | Start      | Finish     | Percent |\n`;
      projectContent += `| ---- | ----------------------------------- | ---------- | ---------- | ------- |\n`;

      // Filter out empty tasks and format with subtask prefix if needed
      project.tasks
        .filter(task => task.task && task.task.trim()) // Only include tasks with content
        .forEach(task => {
          const done = task.done || '[ ]';
          const taskName = task.isSubtask ? `- ${task.task}` : task.task;
          const start = task.start || '';
          const finish = task.finish || '';
          const percent = task.percent || '0';

          projectContent += `| ${done} | ${taskName.padEnd(35)} | ${start.padEnd(10)} | ${finish.padEnd(10)} | ${percent.padEnd(7)} |\n`;
        });

      return projectContent + '\n';
    }).join('');

    return content;
  };

  const handleSave = () => {
    const markdownContent = generateMarkdownContent();
    onSave(markdownContent);
  };

  const addProject = () => {
    setProjects([
      ...projects, 
      { 
        name: 'New Project', 
        color: 'gray', 
        tasks: [] 
      }
    ]);
  };

  const addTask = (projectIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks.push({
      done: '[ ]',
      task: '',
      start: '',
      finish: '',
      percent: '0',
      isSubtask: false
    });
    setProjects(newProjects);
  };

  // Add a function to add a subtask directly after a specific task
  const addSubtask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    const parentTask = newProjects[projectIndex].tasks[taskIndex];

    // Add the new subtask right after the selected task
    const insertPosition = taskIndex + 1;

    newProjects[projectIndex].tasks.splice(insertPosition, 0, {
      done: '[ ]',
      task: '',
      start: parentTask.start || '', // Inherit start date from parent
      finish: parentTask.finish || '', // Inherit finish date from parent
      percent: '0',
      isSubtask: true
    });

    setProjects(newProjects);
  };

  const updateProject = (projectIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex][field] = value;
    setProjects(newProjects);
  };

  const updateTask = (projectIndex, taskIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks[taskIndex][field] = value;
    setProjects(newProjects);
  };

  // Add a new function to convert a task to/from subtask
  const toggleSubtask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    const task = newProjects[projectIndex].tasks[taskIndex];
    task.isSubtask = !task.isSubtask;
    setProjects(newProjects);
  };

  const deleteProject = (projectIndex) => {
    const newProjects = projects.filter((_, index) => index !== projectIndex);
    setProjects(newProjects);
  };

  const deleteTask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks = newProjects[projectIndex].tasks.filter((_, index) => index !== taskIndex);
    setProjects(newProjects);
  };

  const containerStyle = {
    maxWidth: '1000px',
    margin: 'auto',
    padding: spacing.lg,
    backgroundColor: theme.background.elevated,
    borderRadius: borderRadius.lg,
    border: `1px solid ${theme.border.light}`,
  };

  const headerStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  };

  const titleStyle = {
    margin: 0,
    color: theme.text.primary,
    fontSize: '1.5rem',
    fontWeight: '600',
  };

  const buttonGroupStyle = {
    display: 'flex',
    gap: spacing.sm,
  };

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>
        <h2 style={titleStyle}>Goals Editor</h2>
        <div style={buttonGroupStyle}>
          <button
            onClick={addProject}
            style={{
              ...components.button.base,
              ...components.button.secondary,
            }}
          >
            Add Project
          </button>
          <button
            onClick={handleSave}
            style={{
              ...components.button.base,
              ...components.button.primary,
            }}
          >
            Save
          </button>
          <button
            onClick={onCancel}
            style={{
              ...components.button.base,
              ...components.button.error,
            }}
          >
            Cancel
          </button>
        </div>
      </div>

      {projects.map((project, projectIndex) => {
        const projectStyle = {
          backgroundColor: theme.background.paper,
          marginBottom: spacing.lg,
          padding: spacing.md,
          borderRadius: borderRadius.lg,
          border: `1px solid ${theme.border.light}`,
          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
        };

        const projectHeaderStyle = {
          display: 'flex',
          alignItems: 'center',
          marginBottom: spacing.sm,
          gap: spacing.sm,
        };

        const projectNameStyle = {
          ...components.input.base,
          flex: 1,
          fontSize: '1.2em',
          fontWeight: 'bold',
        };

        const colorInputStyle = {
          ...components.input.base,
          width: '100px',
        };

        const deleteProjectButtonStyle = {
          ...components.button.base,
          ...components.button.error,
        };

        return (
          <div key={projectIndex} style={projectStyle}>
            <div style={projectHeaderStyle}>
              <input
                value={project.name}
                onChange={(e) => updateProject(projectIndex, 'name', e.target.value)}
                placeholder="Project Name"
                style={projectNameStyle}
              />
              <input
                value={project.color}
                onChange={(e) => updateProject(projectIndex, 'color', e.target.value)}
                placeholder="Project Color"
                style={colorInputStyle}
              />
              <button
                onClick={() => deleteProject(projectIndex)}
                style={deleteProjectButtonStyle}
              >
                Delete Project
              </button>
            </div>

          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            backgroundColor: theme.background.paper,
            color: theme.text.primary,
          }}>
            <thead>
              <tr style={{ backgroundColor: theme.background.elevated }}>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Done</th>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Subtask</th>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Task</th>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Start</th>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Finish</th>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Percent</th>
                <th style={{ border: `1px solid ${theme.border.light}`, padding: spacing.sm, color: theme.text.primary }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {project.tasks.map((task, taskIndex) => {
                const cellStyle = {
                  border: `1px solid ${theme.border.light}`,
                  padding: spacing.sm,
                  backgroundColor: theme.background.paper,
                };

                const inputStyle = {
                  ...components.input.base,
                  width: '100%',
                  border: 'none',
                  backgroundColor: 'transparent',
                };

                const selectStyle = {
                  ...components.input.base,
                  border: 'none',
                  backgroundColor: 'transparent',
                };

                return (
                  <tr key={taskIndex} style={{
                    ...(task.isSubtask ? {
                      backgroundColor: `${theme.background.elevated}80`,
                      opacity: 0.9
                    } : {})
                  }}>
                    <td style={cellStyle}>
                      <select
                        value={task.done}
                        onChange={(e) => updateTask(projectIndex, taskIndex, 'done', e.target.value)}
                        style={selectStyle}
                      >
                        <option value="[ ]">Incomplete</option>
                        <option value="[x]">Complete</option>
                      </select>
                    </td>
                    <td style={{ ...cellStyle, textAlign: 'center' }}>
                      <select
                        value={task.isSubtask ? 'subtask' : 'main'}
                        onChange={(e) => toggleSubtask(projectIndex, taskIndex)}
                        style={selectStyle}
                      >
                        <option value="main">Main Task</option>
                        <option value="subtask">Subtask</option>
                      </select>
                    </td>
                    <td style={{...cellStyle, paddingLeft: task.isSubtask ? '20px' : '5px'}}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        {task.isSubtask && (
                          <span style={{
                            marginRight: '8px',
                            color: theme.text.secondary,
                            fontSize: '1.2em'
                          }}>
                            ↳
                          </span>
                        )}
                        <input
                          value={task.task}
                          onChange={(e) => updateTask(projectIndex, taskIndex, 'task', e.target.value)}
                          style={inputStyle}
                          placeholder={task.isSubtask ? "Enter subtask..." : "Enter main task..."}
                        />
                      </div>
                    </td>
                    <td style={cellStyle}>
                      <input
                        type="date"
                        value={task.start}
                        onChange={(e) => updateTask(projectIndex, taskIndex, 'start', e.target.value)}
                        style={inputStyle}
                      />
                    </td>
                    <td style={cellStyle}>
                      <input
                        type="date"
                        value={task.finish}
                        onChange={(e) => updateTask(projectIndex, taskIndex, 'finish', e.target.value)}
                        style={inputStyle}
                      />
                    </td>
                    <td style={cellStyle}>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={task.percent}
                        onChange={(e) => updateTask(projectIndex, taskIndex, 'percent', e.target.value)}
                        style={{ ...inputStyle, width: '60px' }}
                      />
                    </td>
                    <td style={{ ...cellStyle, textAlign: 'center' }}>
                      <button
                        onClick={() => deleteTask(projectIndex, taskIndex)}
                        style={{
                          ...components.button.base,
                          ...components.button.error,
                          padding: '3px 6px',
                          fontSize: '0.8rem',
                        }}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginTop: spacing.sm,
            gap: spacing.sm
          }}>
            <button
              onClick={() => addTask(projectIndex)}
              style={{
                ...components.button.base,
                ...components.button.secondary,
                flex: 1
              }}
            >
              Add Main Task
            </button>
            <button
              onClick={() => {
                const newProjects = [...projects];
                newProjects[projectIndex].tasks.push({
                  done: '[ ]',
                  task: '',
                  start: '',
                  finish: '',
                  percent: '0',
                  isSubtask: true
                });
                setProjects(newProjects);
              }}
              style={{
                ...components.button.base,
                ...components.button.secondary,
                flex: 1
              }}
            >
              Add Subtask
            </button>
          </div>
        </div>
      );
    })}
  </div>
);
};

export default GoalsEditor;
