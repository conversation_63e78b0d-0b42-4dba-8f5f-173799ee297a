# GOALS

Welcome to your Daily Notes App! This is an example goals file to demonstrate the application's features.

## Current Projects

### Learning & Development
- **React Development**: Building modern web applications
- **Data Visualization**: Creating interactive charts and graphs
- **Personal Productivity**: Optimizing daily workflows

### Health & Wellness
- **Exercise Routine**: Maintaining regular physical activity
- **Mindfulness Practice**: Daily meditation and reflection
- **Nutrition Goals**: Healthy eating habits

# DATA

## Project: React Development
- ProjectColor: blue
| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Complete React Tutorial             | 2025-07-01 | 2025-07-08 | 100     |
| [ ]  | Build Portfolio Website             | 2025-07-08 | 2025-08-01 | 0       |

## Project: Health & Wellness
- ProjectColor: green
| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Daily 30min Walk                    | 2025-07-01 | 2025-08-01 | 100     |
| [ ]  | Weekly Meal Prep                    | 2025-07-01 | 2026-07-01 | 25      |

## Project: Personal Productivity
- ProjectColor: purple
| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Setup Daily Notes System            | 2025-07-01 | 2025-07-01 | 100     |
| [ ]  | Organize Digital Files              | 2025-07-08 | 2025-08-01 | 0       |

# NOTES

This goals file demonstrates:
1. **Project Planning**: Organize your goals into projects and tasks
2. **Timeline Visualization**: See your progress on interactive charts
3. **Status Tracking**: Monitor what's complete, in progress, or planned
4. **Data-Driven Insights**: Use the DATA section for timeline charts

## How to Use This App

1. **Select a Folder**: Choose a folder containing your markdown files
2. **Edit Goals**: Click "Edit Goals" to modify your project timeline
3. **Daily Notes**: Create and edit daily notes with markdown support
4. **Visual Timeline**: View your progress in the interactive charts

## Tips for Success

- Use consistent date formats (YYYY-MM-DD) in the DATA section
- Keep your daily notes organized by date
- Review and update your goals regularly
- Use the preview feature when editing notes

Happy note-taking! 📝✨