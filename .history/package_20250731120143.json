{"name": "my-daily-notes", "version": "0.1.0", "private": true, "homepage": "./", "main": "main.js", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "plotly.js-dist-min": "^3.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"concurrently": "^9.1.2", "electron": "^36.3.2", "electron-builder": "^26.0.12", "wait-on": "^8.0.3"}, "scripts": {"start": "concurrently \"npm run start-react\" \"npm run start-electron\"", "start-react": "react-scripts start", "start-electron": "wait-on http://localhost:3000 && electron .", "build": "react-scripts build", "build-react": "react-scripts build", "build-app": "npm run build-react && electron-builder --win nsis --x64", "build-win": "npm run build-react && electron-builder --win nsis --x64", "test": "react-scripts test", "eject": "react-scripts eject"}, "build": {"appId": "com.yourname.daily-notes", "extraMetadata": {"main": "main.js"}, "files": ["build/**/*", "main.js", "public/**/*"], "directories": {"buildResources": "assets"}, "win": {"target": ["nsis"], "icon": "assets/icon.ico"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(plotly.js-dist-min)/)"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}